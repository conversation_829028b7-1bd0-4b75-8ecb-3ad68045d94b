-- GPA分析功能测试数据生成脚本（最终版）
-- 使用正确的学号格式，确保学期转换正确
-- 字段说明：WYBS=主键, XH=学号, XNXQ=学年学期, GPA=绩点, JQPJF=加权平均分, SSPJF=算术平均分

-- 清理现有测试数据（可选）
-- DELETE FROM T_GXXS_BKSXQJDXX WHERE XH LIKE '2019%';
-- DELETE FROM TB_ACADEMIC_WARNING WHERE XH LIKE '2019%';

-- =====================================================
-- 测试组1：预警学生当前大一下（2019-2020-2）
-- =====================================================

-- 预警学生：201910001（2019年入学）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('201910001_001', '201910001', '2019-2020-1', 3.2, 92.50, 91.80, 20.0, 20.0, 0.0, '1'),
('201910001_002', '201910001', '2019-2020-2', 3.1, 91.20, 90.50, 18.5, 18.5, 0.0, '1');

-- 榜样学生：201910002（2019年入学，对应榜样）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('201910002_001', '201910002', '2019-2020-1', 3.8, 95.20, 94.80, 20.0, 20.0, 0.0, '1'),
('201910002_002', '201910002', '2019-2020-2', 3.9, 96.10, 95.60, 18.5, 18.5, 0.0, '1'),
('201910002_003', '201910002', '2020-2021-1', 4.0, 97.30, 96.80, 19.0, 19.0, 0.0, '1'),
('201910002_004', '201910002', '2020-2021-2', 3.9, 96.50, 96.20, 18.0, 18.0, 0.0, '1'),
('201910002_005', '201910002', '2021-2022-1', 4.0, 97.80, 97.40, 19.5, 19.5, 0.0, '1'),
('201910002_006', '201910002', '2021-2022-2', 4.0, 98.10, 97.90, 18.0, 18.0, 0.0, '1'),
('201910002_007', '201910002', '2022-2023-1', 3.9, 97.20, 96.90, 17.5, 17.5, 0.0, '1'),
('201910002_008', '201910002', '2022-2023-2', 4.0, 98.50, 98.20, 16.0, 16.0, 0.0, '1');

-- 相似学生：201910003（2019年入学，对应相似）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('201910003_001', '201910003', '2019-2020-1', 3.3, 92.80, 92.10, 20.0, 20.0, 0.0, '1'),
('201910003_002', '201910003', '2019-2020-2', 3.4, 93.40, 92.90, 18.5, 18.5, 0.0, '1'),
('201910003_003', '201910003', '2020-2021-1', 3.6, 94.70, 94.10, 19.0, 19.0, 0.0, '1'),
('201910003_004', '201910003', '2020-2021-2', 3.7, 95.10, 94.50, 18.0, 18.0, 0.0, '1'),
('201910003_005', '201910003', '2021-2022-1', 3.8, 95.90, 95.30, 19.5, 19.5, 0.0, '1'),
('201910003_006', '201910003', '2021-2022-2', 3.8, 96.20, 95.80, 18.0, 18.0, 0.0, '1'),
('201910003_007', '201910003', '2022-2023-1', 3.7, 95.50, 95.10, 17.5, 17.5, 0.0, '1'),
('201910003_008', '201910003', '2022-2023-2', 3.9, 96.80, 96.40, 16.0, 16.0, 0.0, '1');

-- 预警信息
INSERT INTO TB_ACADEMIC_WARNING (WYBS, XH, WARNING_TYPE, WARNING_STATUS, SIMILARITY_STUDENT_XH, EXAMPLE_STUDENT_XH, EXAMPLE_DIFFERENCES, GUIDANCE) VALUES
('201910001_WARNING', '201910001', '01', 1, '201910003', '201910002', '榜样学长大一下平均绩点为3.9，您当前为3.1', '建议加强学习，参考榜样学长的学习方法');

-- =====================================================
-- 测试组2：预警学生当前大二上（2020-2021-1）
-- =====================================================

-- 预警学生：201910004（2019年入学）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('201910004_001', '201910004', '2019-2020-1', 3.0, 90.20, 89.80, 20.0, 20.0, 0.0, '1'),
('201910004_002', '201910004', '2019-2020-2', 2.9, 89.50, 89.10, 18.5, 18.5, 0.0, '1'),
('201910004_003', '201910004', '2020-2021-1', 3.1, 91.30, 90.90, 19.0, 19.0, 0.0, '1');

-- 榜样学生：201910005
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('201910005_001', '201910005', '2019-2020-1', 3.9, 96.20, 95.80, 20.0, 20.0, 0.0, '1'),
('201910005_002', '201910005', '2019-2020-2', 4.0, 97.10, 96.60, 18.5, 18.5, 0.0, '1'),
('201910005_003', '201910005', '2020-2021-1', 4.0, 97.80, 97.40, 19.0, 19.0, 0.0, '1'),
('201910005_004', '201910005', '2020-2021-2', 3.9, 96.90, 96.50, 18.0, 18.0, 0.0, '1'),
('201910005_005', '201910005', '2021-2022-1', 4.0, 98.20, 97.80, 19.5, 19.5, 0.0, '1'),
('201910005_006', '201910005', '2021-2022-2', 4.0, 98.50, 98.10, 18.0, 18.0, 0.0, '1'),
('201910005_007', '201910005', '2022-2023-1', 3.9, 97.60, 97.20, 17.5, 17.5, 0.0, '1'),
('201910005_008', '201910005', '2022-2023-2', 4.0, 98.80, 98.40, 16.0, 16.0, 0.0, '1');

-- 相似学生：201910006
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('201910006_001', '201910006', '2019-2020-1', 3.1, 91.20, 90.80, 20.0, 20.0, 0.0, '1'),
('201910006_002', '201910006', '2019-2020-2', 3.2, 92.40, 91.90, 18.5, 18.5, 0.0, '1'),
('201910006_003', '201910006', '2020-2021-1', 3.5, 94.30, 93.80, 19.0, 19.0, 0.0, '1'),
('201910006_004', '201910006', '2020-2021-2', 3.6, 94.80, 94.30, 18.0, 18.0, 0.0, '1'),
('201910006_005', '201910006', '2021-2022-1', 3.7, 95.60, 95.10, 19.5, 19.5, 0.0, '1'),
('201910006_006', '201910006', '2021-2022-2', 3.8, 96.10, 95.60, 18.0, 18.0, 0.0, '1'),
('201910006_007', '201910006', '2022-2023-1', 3.7, 95.40, 94.90, 17.5, 17.5, 0.0, '1'),
('201910006_008', '201910006', '2022-2023-2', 3.8, 96.50, 96.00, 16.0, 16.0, 0.0, '1');

-- 预警信息
INSERT INTO TB_ACADEMIC_WARNING (WYBS, XH, WARNING_TYPE, WARNING_STATUS, SIMILARITY_STUDENT_XH, EXAMPLE_STUDENT_XH, EXAMPLE_DIFFERENCES, GUIDANCE) VALUES
('201910004_WARNING', '201910004', '01', 1, '201910006', '201910005', '榜样学长大二上平均绩点为4.0，您当前为3.1', '建议制定学习计划，向榜样学长学习');

-- =====================================================
-- 测试组3：预警学生当前大三上（2021-2022-1）
-- =====================================================

-- 预警学生：201910007（2019年入学）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('201910007_001', '201910007', '2019-2020-1', 2.8, 88.20, 87.80, 20.0, 20.0, 0.0, '1'),
('201910007_002', '201910007', '2019-2020-2', 2.9, 89.10, 88.60, 18.5, 18.5, 0.0, '1'),
('201910007_003', '201910007', '2020-2021-1', 3.0, 90.30, 89.90, 19.0, 19.0, 0.0, '1'),
('201910007_004', '201910007', '2020-2021-2', 3.1, 91.20, 90.80, 18.0, 18.0, 0.0, '1'),
('201910007_005', '201910007', '2021-2022-1', 3.2, 92.10, 91.70, 19.5, 19.5, 0.0, '1');

-- 榜样学生：201910008
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('201910008_001', '201910008', '2019-2020-1', 3.7, 94.20, 93.80, 20.0, 20.0, 0.0, '1'),
('201910008_002', '201910008', '2019-2020-2', 3.8, 95.10, 94.60, 18.5, 18.5, 0.0, '1'),
('201910008_003', '201910008', '2020-2021-1', 3.9, 96.30, 95.90, 19.0, 19.0, 0.0, '1'),
('201910008_004', '201910008', '2020-2021-2', 4.0, 97.20, 96.80, 18.0, 18.0, 0.0, '1'),
('201910008_005', '201910008', '2021-2022-1', 4.0, 98.10, 97.70, 19.5, 19.5, 0.0, '1'),
('201910008_006', '201910008', '2021-2022-2', 3.9, 97.50, 97.10, 18.0, 18.0, 0.0, '1'),
('201910008_007', '201910008', '2022-2023-1', 4.0, 98.40, 98.00, 17.5, 17.5, 0.0, '1'),
('201910008_008', '201910008', '2022-2023-2', 4.0, 98.90, 98.50, 16.0, 16.0, 0.0, '1');

-- 相似学生：201910009
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('201910009_001', '201910009', '2019-2020-1', 2.9, 89.20, 88.80, 20.0, 20.0, 0.0, '1'),
('201910009_002', '201910009', '2019-2020-2', 3.0, 90.40, 89.90, 18.5, 18.5, 0.0, '1'),
('201910009_003', '201910009', '2020-2021-1', 3.3, 92.30, 91.80, 19.0, 19.0, 0.0, '1'),
('201910009_004', '201910009', '2020-2021-2', 3.4, 93.20, 92.70, 18.0, 18.0, 0.0, '1'),
('201910009_005', '201910009', '2021-2022-1', 3.6, 94.60, 94.10, 19.5, 19.5, 0.0, '1'),
('201910009_006', '201910009', '2021-2022-2', 3.7, 95.10, 94.60, 18.0, 18.0, 0.0, '1'),
('201910009_007', '201910009', '2022-2023-1', 3.6, 94.40, 93.90, 17.5, 17.5, 0.0, '1'),
('201910009_008', '201910009', '2022-2023-2', 3.8, 95.80, 95.30, 16.0, 16.0, 0.0, '1');

-- 预警信息
INSERT INTO TB_ACADEMIC_WARNING (WYBS, XH, WARNING_TYPE, WARNING_STATUS, SIMILARITY_STUDENT_XH, EXAMPLE_STUDENT_XH, EXAMPLE_DIFFERENCES, GUIDANCE) VALUES
('201910007_WARNING', '201910007', '01', 1, '201910009', '201910008', '榜样学长大三上平均绩点为4.0，您当前为3.2', '建议重点关注专业课程，提升学习效率');

-- =====================================================
-- 清理测试数据的SQL（测试完成后使用）
-- =====================================================
/*
-- 清理测试数据
DELETE FROM T_GXXS_BKSXQJDXX WHERE XH LIKE '2019%';
DELETE FROM TB_ACADEMIC_WARNING WHERE XH LIKE '2019%';
*/
