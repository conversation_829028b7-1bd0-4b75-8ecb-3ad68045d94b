2025-07-31 09:24:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 09:24:02 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 11068 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 09:24:02 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 09:24:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 09:24:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 09:24:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-07-31 09:24:04 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=05392083-818a-3fcf-948d-05036ee1e940
2025-07-31 09:24:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 09:24:04 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2477 ms
2025-07-31 09:24:06 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 09:24:06 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 09:24:06 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 09:24:06 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 09:24:08 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 09:24:08 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 09:24:08 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 09:24:08 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 09:24:08 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 09:24:08 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8081 (http) with context path '/'
2025-07-31 09:24:08 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.585 seconds (process running for 8.96)
2025-07-31 09:24:08 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8081/swagger-ui/index.html
2025-07-31 09:24:09 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 09:24:09 [RMI TCP Connection(3)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 09:24:09 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 09:24:09 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-31 09:24:13 [XNIO-1 task-5] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1045 ms
2025-07-31 09:24:14 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@f9ab7a
2025-07-31 09:24:14 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 09:25:26 [XNIO-1 task-5] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 发送聊天消息，用户: ewps-system, 会话ID: , 查询内容: 学生情绪不佳该如何解决？
2025-07-31 10:13:06 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 10:13:06 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 36196 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 10:13:06 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 10:13:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 10:13:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 10:13:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-07-31 10:13:07 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07258a08-5d54-3ead-a5cd-03abf1d878c4
2025-07-31 10:13:08 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 10:13:08 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2436 ms
2025-07-31 10:13:10 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 10:13:10 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 10:13:10 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 10:13:10 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 10:13:12 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 10:13:12 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 10:13:12 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 10:13:12 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 10:13:12 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 10:13:12 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 10:13:12 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 10:15:39 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 10:15:39 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 22028 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 10:15:39 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 10:15:42 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 10:15:42 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 10:15:42 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 43 ms. Found 0 Redis repository interfaces.
2025-07-31 10:15:42 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07258a08-5d54-3ead-a5cd-03abf1d878c4
2025-07-31 10:15:43 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 10:15:43 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3530 ms
2025-07-31 10:15:44 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 10:15:45 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 10:15:45 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 10:15:45 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 10:15:46 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 10:15:47 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 10:15:47 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 10:15:47 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 10:15:47 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 10:15:47 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 10:15:47 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 10:16:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 10:16:02 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 32008 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 10:16:02 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 10:16:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 10:16:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 10:16:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-07-31 10:16:04 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07258a08-5d54-3ead-a5cd-03abf1d878c4
2025-07-31 10:16:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 10:16:04 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2488 ms
2025-07-31 10:16:06 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 10:16:06 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 10:16:06 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 10:16:06 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 10:16:08 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 10:16:08 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 10:16:08 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 10:16:08 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 10:16:08 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 10:16:08 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-31 10:16:08 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.477 seconds (process running for 7.471)
2025-07-31 10:16:08 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-07-31 10:16:09 [RMI TCP Connection(3)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 10:16:09 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 10:16:09 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-31 10:16:09 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 10:16:12 [XNIO-1 task-4] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 992 ms
2025-07-31 10:16:13 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@63b3b187
2025-07-31 10:16:13 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 10:16:25 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-31 10:24:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-31 10:24:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 10:24:58 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 40708 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 10:24:58 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 10:24:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 10:24:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 10:24:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-07-31 10:25:00 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07264997-8967-33d4-b996-4af8babf5a24
2025-07-31 10:25:00 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 10:25:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2276 ms
2025-07-31 10:25:02 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 10:25:02 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 10:25:02 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 10:25:02 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 10:25:04 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 10:25:04 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 10:25:04 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 10:25:04 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 10:25:04 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 10:25:04 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-31 10:25:04 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.421 seconds (process running for 7.309)
2025-07-31 10:25:04 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-07-31 10:25:05 [RMI TCP Connection(2)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 10:25:05 [RMI TCP Connection(2)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 10:25:05 [RMI TCP Connection(2)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-31 10:25:05 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 10:25:08 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1004 ms
2025-07-31 10:25:09 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@71e07016
2025-07-31 10:25:09 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 10:25:25 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:28:28 [RMI TCP Connection(8)-***************] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: iai2, 会话ID: null, 查询内容: 大学生情绪不佳怎么办？
2025-07-31 10:28:34 [RMI TCP Connection(8)-***************] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: iai2, 会话ID: null, 查询内容: 大学生情绪不佳怎么办？
2025-07-31 10:28:44 [RMI TCP Connection(8)-***************] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: iai2, 会话ID: null, 查询内容: 大学生情绪不佳怎么办？
2025-07-31 10:29:00 [RMI TCP Connection(8)-***************] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: iai2, 会话ID: null, 查询内容: 大学生情绪不佳怎么办？
2025-07-31 10:29:46 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: flux-test-user, 会话ID: null, 查询内容: 111
2025-07-31 10:30:23 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: flux-test-user, 会话ID: null, 查询内容: 你好
2025-07-31 10:30:55 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: flux-test-user, 会话ID: null, 查询内容: 你好
2025-07-31 10:32:06 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: web-user, 会话ID: null, 查询内容: 你好
2025-07-31 10:36:49 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 10:36:49 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-31 10:36:49 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 10:36:49 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 10:36:51 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-31 10:36:51 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-31 10:36:56 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 10:36:56 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 40332 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 10:36:56 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 10:36:58 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 10:36:58 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 10:36:58 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-07-31 10:36:58 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07264997-8967-33d4-b996-4af8babf5a24
2025-07-31 10:36:59 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 10:36:59 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2354 ms
2025-07-31 10:37:00 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 10:37:01 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 10:37:01 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 10:37:01 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 10:37:02 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 10:37:02 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 10:37:02 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 10:37:02 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 10:37:02 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 10:37:02 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-31 10:37:03 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.677 seconds (process running for 7.423)
2025-07-31 10:37:03 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-07-31 10:37:03 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 10:37:03 [RMI TCP Connection(4)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 10:37:03 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 10:37:03 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-31 10:37:08 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5ff979c8
2025-07-31 10:37:08 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 10:37:49 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: flux-test-user, 会话ID: null, 查询内容: 你好
2025-07-31 10:38:18 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: flux-test-user, 会话ID: d57a4423-ed39-41f1-ab54-3e1ba402f3c7, 查询内容: 大学生情绪不佳该如何处理？
2025-07-31 10:40:17 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1690 ms
2025-07-31 10:40:45 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:45:03 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:46:36 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:46:55 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: ewps-system, 会话ID: string, 查询内容: 请扮演一个经验丰富的大学辅导员，帮助学生解决一些大学生活中遇到的问题。
2025-07-31 10:47:32 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: apifox-test-user, 会话ID: , 查询内容: 你好，请介绍一下你自己
2025-07-31 10:56:36 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.StreamChatServiceImpl - 开始流式聊天，用户: apifox-test-user, 会话ID: , 查询内容: 你好，请介绍一下你自己
2025-07-31 14:51:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 14:51:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-31 14:51:54 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 14:51:54 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 14:51:56 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-31 14:52:02 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-31 14:52:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 14:52:21 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 10032 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 14:52:21 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 14:52:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 14:52:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 14:52:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-07-31 14:52:23 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07264997-8967-33d4-b996-4af8babf5a24
2025-07-31 14:52:24 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 14:52:24 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2857 ms
2025-07-31 14:52:26 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 14:52:56 [redisson-netty-1-18] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 14:52:56 [redisson-netty-1-2] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 14:52:56 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 14:52:59 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 14:52:59 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 14:52:59 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 14:52:59 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 14:52:59 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 14:52:59 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 14:52:59 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 14:53:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 14:53:18 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 38392 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 14:53:18 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 14:53:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 14:53:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 14:53:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-07-31 14:53:19 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07264997-8967-33d4-b996-4af8babf5a24
2025-07-31 14:53:20 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 14:53:20 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2462 ms
2025-07-31 14:53:22 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 14:53:22 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 14:53:22 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 14:53:22 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 14:53:24 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 14:53:24 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 14:53:24 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 14:53:24 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 14:53:24 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 14:53:24 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-31 14:53:24 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.925 seconds (process running for 7.83)
2025-07-31 14:53:24 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-07-31 14:53:25 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 14:53:25 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 14:53:25 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-31 14:53:25 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 14:53:28 [XNIO-1 task-5] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1086 ms
2025-07-31 14:53:30 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@1fb3fbe6
2025-07-31 14:53:30 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-31 15:03:08 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-31 15:03:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 15:03:13 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 36224 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 15:03:13 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 15:03:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 15:03:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 15:03:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-07-31 15:03:15 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07264997-8967-33d4-b996-4af8babf5a24
2025-07-31 15:03:16 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 15:03:16 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2653 ms
2025-07-31 15:03:17 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 15:03:18 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 15:03:18 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 15:03:18 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 15:03:20 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 15:03:20 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 15:03:20 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 15:03:20 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 15:03:20 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 15:03:20 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-31 15:03:20 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.301 seconds (process running for 8.186)
2025-07-31 15:03:20 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-07-31 15:03:21 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 15:03:21 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 15:03:21 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-31 15:03:21 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 15:03:25 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@497d15e9
2025-07-31 15:03:25 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 15:04:41 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 959 ms
2025-07-31 15:21:39 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 15:21:39 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-31 15:21:39 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 15:21:39 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 15:21:39 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-31 15:21:39 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-31 15:21:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-31 15:21:44 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 44288 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-31 15:21:44 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-31 15:21:45 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 15:21:45 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 15:21:45 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-07-31 15:21:46 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-07-31 15:21:47 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 15:21:47 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2517 ms
2025-07-31 15:21:48 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-31 15:21:48 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-31 15:21:49 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-31 15:21:49 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-31 15:21:50 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-31 15:21:50 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-31 15:21:50 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-31 15:21:50 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-31 15:21:51 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 15:21:51 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-31 15:21:51 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.155 seconds (process running for 7.909)
2025-07-31 15:21:51 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-07-31 15:21:51 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 15:21:51 [RMI TCP Connection(4)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 15:21:51 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 15:21:51 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-31 15:21:53 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1058 ms
2025-07-31 15:21:56 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@79ed8f05
2025-07-31 15:21:56 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 16:42:44 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-31 16:42:44 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-31 16:42:44 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-31 16:42:44 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 16:42:44 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-31 16:42:44 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-01 14:15:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 14:15:19 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 10832 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-01 14:15:19 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-01 14:15:20 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:15:20 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:15:20 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-08-01 14:15:21 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-08-01 14:15:21 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:15:21 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2717 ms
2025-08-01 14:15:23 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 14:15:23 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-01 14:15:24 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-01 14:15:24 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-01 14:15:25 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-01 14:15:25 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 14:15:25 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 14:15:25 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 14:15:25 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:15:25 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-01 14:15:25 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.265 seconds (process running for 8.382)
2025-08-01 14:15:25 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-01 14:15:26 [RMI TCP Connection(4)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:15:26 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:15:26 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-01 14:15:26 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 14:15:29 [XNIO-1 task-6] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1049 ms
2025-08-01 14:15:31 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5cc848ef
2025-08-01 14:15:31 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-01 14:16:59 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 14:16:59 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-01 14:16:59 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 14:16:59 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:16:59 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-01 14:16:59 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-01 14:19:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 14:19:04 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 42632 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-01 14:19:04 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-01 14:19:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:19:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:19:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-08-01 14:19:06 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-08-01 14:19:06 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:19:06 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2486 ms
2025-08-01 14:19:08 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 14:19:08 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-01 14:19:08 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-01 14:19:09 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-01 14:19:10 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-01 14:19:10 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 14:19:10 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 14:19:10 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 14:19:10 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:19:10 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-01 14:19:10 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.005 seconds (process running for 7.887)
2025-08-01 14:19:10 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-01 14:19:11 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:19:11 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:19:11 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 14:19:11 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 14:19:16 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@65629da2
2025-08-01 14:19:16 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-01 14:19:58 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1220 ms
2025-08-01 14:24:42 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 14:24:42 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-01 14:24:42 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 14:24:42 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:24:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-01 14:24:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-01 14:24:47 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 14:24:47 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 42896 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-01 14:24:47 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-01 14:24:48 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:24:48 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:24:48 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-08-01 14:24:48 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-08-01 14:24:49 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:24:49 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2433 ms
2025-08-01 14:24:51 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 14:24:51 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-01 14:24:51 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-01 14:24:52 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-01 14:24:53 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-01 14:24:53 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 14:24:53 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 14:24:53 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 14:24:53 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:24:53 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-01 14:24:53 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.119 seconds (process running for 7.896)
2025-08-01 14:24:53 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-01 14:24:54 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:24:54 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:24:54 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 14:24:54 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 14:24:59 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@1102d707
2025-08-01 14:24:59 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-01 14:29:52 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 14:29:52 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-01 14:29:52 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 14:29:52 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:29:52 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-01 14:29:52 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-01 14:29:54 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 14:29:54 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 28084 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-01 14:29:54 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-01 14:29:56 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:29:56 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:29:56 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-08-01 14:29:56 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-08-01 14:29:57 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:29:57 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2494 ms
2025-08-01 14:29:58 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 14:29:59 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-01 14:29:59 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-01 14:29:59 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-01 14:30:00 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-01 14:30:00 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 14:30:00 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 14:30:00 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 14:30:00 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:30:00 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-01 14:30:01 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.922 seconds (process running for 7.835)
2025-08-01 14:30:01 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-01 14:30:01 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:30:01 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:30:01 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 14:30:01 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 14:30:06 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@3c95e4c8
2025-08-01 14:30:06 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-01 14:33:06 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 14:33:06 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-01 14:33:06 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 14:33:06 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:33:06 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-01 14:33:06 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-01 14:33:08 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 14:33:08 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 20028 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-01 14:33:08 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-01 14:33:09 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:33:09 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:33:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-08-01 14:33:10 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-08-01 14:33:11 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:33:11 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2342 ms
2025-08-01 14:33:12 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 14:33:12 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-01 14:33:13 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-01 14:33:13 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-01 14:33:14 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-01 14:33:14 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 14:33:14 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 14:33:14 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 14:33:14 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:33:14 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-01 14:33:15 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.794 seconds (process running for 7.782)
2025-08-01 14:33:15 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-01 14:33:15 [RMI TCP Connection(3)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:33:15 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:33:15 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-01 14:33:15 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 14:33:20 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@393ff32d
2025-08-01 14:33:20 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-01 14:33:20 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1009 ms
2025-08-01 14:35:42 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 14:35:42 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-01 14:35:42 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 14:35:42 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:35:42 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-01 14:35:42 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-01 14:35:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 14:35:44 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 31692 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-01 14:35:44 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-01 14:35:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:35:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:35:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-08-01 14:35:46 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-08-01 14:35:47 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:35:47 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2636 ms
2025-08-01 14:35:48 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 14:35:49 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-01 14:35:49 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-01 14:35:49 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-01 14:35:51 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-01 14:35:51 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 14:35:51 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 14:35:51 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 14:35:51 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:35:51 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-01 14:35:51 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.085 seconds (process running for 7.919)
2025-08-01 14:35:51 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-01 14:35:51 [RMI TCP Connection(3)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:35:51 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:35:51 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-08-01 14:35:51 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 14:35:56 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@12411452
2025-08-01 14:35:56 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-01 14:36:06 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1757 ms
2025-08-01 14:39:23 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 14:39:23 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-01 14:39:23 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 14:39:23 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:39:23 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-01 14:39:23 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-01 14:39:25 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 14:39:25 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 9740 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-01 14:39:25 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-01 14:39:27 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:39:27 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:39:27 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-08-01 14:39:27 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-08-01 14:39:28 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:39:28 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2631 ms
2025-08-01 14:39:30 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 14:39:30 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-01 14:39:30 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-01 14:39:30 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-01 14:39:32 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-01 14:39:32 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 14:39:32 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 14:39:32 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 14:39:32 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:39:32 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-01 14:39:32 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.195 seconds (process running for 8.228)
2025-08-01 14:39:32 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-01 14:39:33 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 14:39:33 [RMI TCP Connection(4)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:39:33 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:39:33 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 14:39:38 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@682de3f6
2025-08-01 14:39:38 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-01 14:42:02 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 14:42:02 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-01 14:42:02 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 14:42:02 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:42:02 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-01 14:42:02 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-01 14:43:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 14:43:18 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 3152 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-01 14:43:18 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-01 14:43:20 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:43:20 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:43:20 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-08-01 14:43:20 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-08-01 14:43:21 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:43:21 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2343 ms
2025-08-01 14:43:22 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 14:43:22 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-01 14:43:23 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-01 14:43:23 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-01 14:43:24 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-01 14:43:24 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 14:43:24 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 14:43:24 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 14:43:24 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:43:24 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-01 14:43:25 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.82 seconds (process running for 7.635)
2025-08-01 14:43:25 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-01 14:43:25 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 14:43:25 [RMI TCP Connection(4)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:43:25 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:43:25 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 14:43:28 [XNIO-1 task-3] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 990 ms
2025-08-01 14:43:30 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@75dd85ff
2025-08-01 14:43:30 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-01 14:50:07 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 14:50:07 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-01 14:50:07 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 14:50:07 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:50:07 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-01 14:50:07 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-01 14:50:09 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 14:50:09 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 36676 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-01 14:50:09 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-01 14:50:11 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:50:11 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:50:11 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-08-01 14:50:11 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-08-01 14:50:12 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:50:12 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2586 ms
2025-08-01 14:50:13 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 14:50:14 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-01 14:50:14 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-01 14:50:14 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-01 14:50:16 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-01 14:50:16 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 14:50:16 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 14:50:16 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 14:50:16 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:50:16 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-01 14:50:16 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.299 seconds (process running for 8.406)
2025-08-01 14:50:16 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-01 14:50:17 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:50:17 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:50:17 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 14:50:17 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 14:50:22 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@6132cee1
2025-08-01 14:50:22 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-01 14:50:23 [XNIO-1 task-3] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1057 ms
2025-08-01 14:53:25 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 14:53:25 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-01 14:53:25 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-01 14:53:25 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:53:26 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-01 14:53:26 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-01 14:53:28 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-01 14:53:28 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 41476 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-01 14:53:28 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-01 14:53:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:53:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:53:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-08-01 14:53:30 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=fdc82168-0142-39ec-93e4-041c15db6d31
2025-08-01 14:53:31 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:53:31 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2689 ms
2025-08-01 14:53:32 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-01 14:53:33 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-01 14:53:33 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-01 14:53:33 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-01 14:53:34 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-01 14:53:35 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-01 14:53:35 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-01 14:53:35 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-01 14:53:35 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:53:35 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-01 14:53:35 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.103 seconds (process running for 8.17)
2025-08-01 14:53:35 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-01 14:53:36 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 14:53:36 [RMI TCP Connection(4)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:53:36 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:53:36 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 14:53:39 [XNIO-1 task-3] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1019 ms
2025-08-01 14:53:40 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5c49990f
2025-08-01 14:53:40 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 09:32:05 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 09:32:05 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-04 09:32:05 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 09:32:05 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 09:32:05 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-04 09:32:05 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-04 14:24:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 14:24:38 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 46860 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-04 14:24:38 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-04 14:24:39 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 14:24:39 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 14:24:40 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-08-04 14:24:40 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=94f79d72-0ee5-3029-b78a-45a5926d2577
2025-08-04 14:24:41 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 14:24:41 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2673 ms
2025-08-04 14:24:42 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 14:24:43 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-04 14:24:43 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-04 14:24:43 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-04 14:24:44 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-04 14:24:44 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-04 14:24:45 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-04 14:24:45 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-04 14:24:45 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-04 14:24:45 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 14:24:45 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 14:25:01 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 14:25:01 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 27312 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-04 14:25:01 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-04 14:25:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 14:25:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 14:25:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-08-04 14:25:03 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=94f79d72-0ee5-3029-b78a-45a5926d2577
2025-08-04 14:25:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 14:25:04 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2418 ms
2025-08-04 14:25:05 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 14:25:05 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-04 14:25:06 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-04 14:25:06 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-04 14:25:07 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-04 14:25:07 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-04 14:25:07 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-04 14:25:07 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-04 14:25:07 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-04 14:25:07 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-04 14:25:08 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.731 seconds (process running for 7.604)
2025-08-04 14:25:08 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://*************:8080/swagger-ui/index.html
2025-08-04 14:25:08 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 14:25:08 [RMI TCP Connection(1)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 14:25:08 [RMI TCP Connection(1)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-04 14:25:08 [RMI TCP Connection(3)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-04 14:25:13 [RMI TCP Connection(3)-*************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5af70b30
2025-08-04 14:25:13 [RMI TCP Connection(3)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 14:25:13 [XNIO-1 task-4] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1046 ms
2025-08-04 14:54:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 14:54:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-04 14:54:54 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 14:54:54 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 14:54:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-04 14:55:25 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 14:55:25 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 46060 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-04 14:55:25 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-04 14:55:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 14:55:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 14:55:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Redis repository interfaces.
2025-08-04 14:55:26 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=94f79d72-0ee5-3029-b78a-45a5926d2577
2025-08-04 14:55:27 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 14:55:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2555 ms
2025-08-04 14:55:29 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 14:55:29 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-04 14:55:29 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-04 14:55:29 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-04 14:55:31 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-04 14:55:31 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-04 14:55:31 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-04 14:55:31 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-04 14:55:31 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-04 14:55:31 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-04 14:55:31 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.078 seconds (process running for 7.936)
2025-08-04 14:55:31 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://*************:8080/swagger-ui/index.html
2025-08-04 14:55:32 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-04 14:55:32 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 14:55:32 [RMI TCP Connection(4)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 14:55:32 [RMI TCP Connection(4)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-04 14:55:36 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@2b72346c
2025-08-04 14:55:36 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 14:55:39 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1013 ms
2025-08-04 14:56:35 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 14:56:35 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-04 14:56:35 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 14:56:35 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 14:56:35 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-04 14:56:35 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-04 15:17:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 15:17:13 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 23492 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-04 15:17:13 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-04 15:17:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 15:17:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:17:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-08-04 15:17:15 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=94f79d72-0ee5-3029-b78a-45a5926d2577
2025-08-04 15:17:16 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 15:17:16 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2470 ms
2025-08-04 15:17:17 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 15:17:18 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-04 15:17:18 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-04 15:17:18 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-04 15:17:19 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-04 15:17:19 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-04 15:17:19 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-04 15:17:19 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-04 15:17:19 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-04 15:17:20 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-04 15:17:20 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.773 seconds (process running for 7.577)
2025-08-04 15:17:20 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://*************:8080/swagger-ui/index.html
2025-08-04 15:17:20 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 15:17:20 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 15:17:20 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-08-04 15:17:20 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-04 15:17:24 [XNIO-1 task-3] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1117 ms
2025-08-04 15:17:25 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@7fde801a
2025-08-04 15:17:25 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 15:55:07 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 15:55:07 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-04 15:55:07 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 15:55:07 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 15:55:07 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-04 15:55:07 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-04 15:57:26 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 15:57:26 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 50580 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-04 15:57:26 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-04 15:57:28 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 15:57:28 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:57:28 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-08-04 15:57:28 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=c427d93e-2d3d-3c86-85e2-43c97394120b
2025-08-04 15:57:29 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 15:57:29 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2455 ms
2025-08-04 15:57:30 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 15:57:31 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-04 15:57:31 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-04 15:57:31 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-04 15:57:32 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-04 15:57:32 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-04 15:57:32 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-04 15:57:32 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-04 15:57:33 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-04 15:57:33 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-04 15:57:33 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.762 seconds (process running for 7.581)
2025-08-04 15:57:33 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://*************:8080/swagger-ui/index.html
2025-08-04 15:57:33 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 15:57:33 [RMI TCP Connection(1)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 15:57:33 [RMI TCP Connection(1)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-04 15:57:33 [RMI TCP Connection(3)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-04 15:57:38 [XNIO-1 task-3] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1146 ms
2025-08-04 15:57:38 [RMI TCP Connection(3)-*************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@6cf462ea
2025-08-04 15:57:38 [RMI TCP Connection(3)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 16:06:17 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 16:06:17 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-04 16:06:17 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 16:06:17 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 16:06:17 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-04 16:06:17 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-04 16:06:22 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 16:06:23 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 50268 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-04 16:06:23 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-04 16:06:24 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 16:06:24 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:06:24 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-08-04 16:06:24 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=c427d93e-2d3d-3c86-85e2-43c97394120b
2025-08-04 16:06:25 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 16:06:25 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2446 ms
2025-08-04 16:06:26 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 16:06:27 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-04 16:06:27 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-04 16:06:27 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-04 16:06:29 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-04 16:06:29 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-04 16:06:29 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-04 16:06:29 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-04 16:06:29 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-04 16:06:29 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-04 16:06:29 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.02 seconds (process running for 7.815)
2025-08-04 16:06:29 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://*************:8080/swagger-ui/index.html
2025-08-04 16:06:30 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 16:06:30 [RMI TCP Connection(3)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 16:06:30 [RMI TCP Connection(3)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-04 16:06:30 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-04 16:06:34 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@6a1ba5e9
2025-08-04 16:06:34 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 16:06:38 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1336 ms
2025-08-04 16:08:58 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 获取会话列表，用户: iai2
2025-08-04 16:08:58 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 获取会话列表成功，会话数量: 8
2025-08-04 16:09:48 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 获取消息历史，用户: iai2, 会话ID: 83686ccf-15af-4c07-a46f-a007d84b64df
2025-08-04 16:09:48 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 获取消息历史成功，消息数量: 1
2025-08-04 16:14:56 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 获取消息历史，用户: iai2, 会话ID: 83686ccf-15af-4c07-a46f-a007d84b64df
2025-08-04 16:14:56 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 获取消息历史成功，消息数量: 1
2025-08-04 16:15:15 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 获取消息历史，用户: iai2, 会话ID: d172b9b0-2c0c-432a-b9d1-c7ca7654d745
2025-08-04 16:15:15 [XNIO-1 task-2] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 获取消息历史成功，消息数量: 1
2025-08-04 16:29:43 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 16:29:43 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-04 16:29:43 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 16:29:43 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 16:29:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-04 16:29:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-04 16:30:34 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 16:30:34 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 7456 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-04 16:30:34 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-04 16:30:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 16:30:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:30:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-08-04 16:30:35 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=c427d93e-2d3d-3c86-85e2-43c97394120b
2025-08-04 16:30:36 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 16:30:36 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2508 ms
2025-08-04 16:30:38 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 16:30:38 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-04 16:30:38 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-04 16:30:38 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-04 16:30:40 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-04 16:30:40 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-04 16:30:40 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-04 16:30:40 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-04 16:30:40 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-04 16:30:40 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-04 16:30:40 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.9 seconds (process running for 7.722)
2025-08-04 16:30:40 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://*************:8080/swagger-ui/index.html
2025-08-04 16:30:41 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 16:30:41 [RMI TCP Connection(3)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 16:30:41 [RMI TCP Connection(3)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-04 16:30:41 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-04 16:30:46 [XNIO-1 task-3] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1048 ms
2025-08-04 16:30:46 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@2bbc40fe
2025-08-04 16:30:46 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 16:37:27 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 16:37:27 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-04 16:37:27 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 16:37:27 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 16:37:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-04 16:37:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-04 16:37:32 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 16:37:32 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 44464 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-04 16:37:32 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-04 16:37:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 16:37:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:37:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-08-04 16:37:33 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=c427d93e-2d3d-3c86-85e2-43c97394120b
2025-08-04 16:37:34 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 16:37:34 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2460 ms
2025-08-04 16:37:36 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 16:37:36 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-04 16:37:36 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-04 16:37:36 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-04 16:37:38 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-04 16:37:38 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-04 16:37:38 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-04 16:37:38 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-04 16:37:38 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-04 16:37:38 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-04 16:37:38 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.933 seconds (process running for 7.79)
2025-08-04 16:37:38 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://*************:8080/swagger-ui/index.html
2025-08-04 16:37:39 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 16:37:39 [RMI TCP Connection(1)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 16:37:39 [RMI TCP Connection(1)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-08-04 16:37:39 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-04 16:37:43 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@3ed7466
2025-08-04 16:37:43 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 16:37:52 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1111 ms
2025-08-04 16:46:04 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 16:46:04 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-04 16:46:04 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 16:46:04 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 16:46:04 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-04 16:46:04 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-04 16:46:09 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 16:46:09 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 49396 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-04 16:46:09 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-04 16:46:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 16:46:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:46:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-08-04 16:46:10 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=c427d93e-2d3d-3c86-85e2-43c97394120b
2025-08-04 16:46:11 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 16:46:11 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2470 ms
2025-08-04 16:46:13 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 16:46:13 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-04 16:46:13 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-04 16:46:13 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-04 16:46:15 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-04 16:46:15 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-04 16:46:15 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-04 16:46:15 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-04 16:46:15 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-04 16:46:15 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-04 16:46:15 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.788 seconds (process running for 7.573)
2025-08-04 16:46:15 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://*************:8080/swagger-ui/index.html
2025-08-04 16:46:15 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 16:46:15 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 16:46:15 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-04 16:46:15 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-04 16:46:19 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1145 ms
2025-08-04 16:46:20 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5756f482
2025-08-04 16:46:20 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 17:41:46 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 17:41:46 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-04 17:41:46 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 17:41:46 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 17:41:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-04 17:41:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-04 17:41:53 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 17:41:53 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 43660 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-04 17:41:53 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-04 17:41:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 17:41:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 17:41:54 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-08-04 17:41:54 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=c427d93e-2d3d-3c86-85e2-43c97394120b
2025-08-04 17:41:55 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 17:41:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2472 ms
2025-08-04 17:41:57 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 17:41:57 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-04 17:41:57 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-04 17:41:57 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-04 17:41:59 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-04 17:41:59 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-04 17:41:59 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-04 17:41:59 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-04 17:41:59 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-04 17:41:59 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-04 17:41:59 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.838 seconds (process running for 7.594)
2025-08-04 17:41:59 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-04 17:41:59 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 17:41:59 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 17:41:59 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-08-04 17:41:59 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-04 17:42:03 [XNIO-1 task-4] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1061 ms
2025-08-04 17:42:04 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@95dbb66
2025-08-04 17:42:04 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 18:02:16 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-04 18:02:16 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-04 18:02:16 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-04 18:02:16 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04 18:02:16 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-04 18:02:16 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-05 10:42:31 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 10:42:31 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 50984 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-05 10:42:31 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-05 10:42:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 10:42:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 10:42:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-08-05 10:42:33 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=736bd39f-f225-30da-b126-c02deb08e181
2025-08-05 10:42:34 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-05 10:42:34 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3516 ms
2025-08-05 10:42:36 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-05 10:42:36 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-05 10:42:37 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-05 10:42:37 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-05 10:42:38 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-05 10:42:38 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-05 10:42:38 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-05 10:42:38 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-05 10:42:38 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-05 10:42:39 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-05 10:42:39 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-05 10:42:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 10:42:52 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 50592 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-05 10:42:52 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-05 10:42:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 10:42:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 10:42:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-08-05 10:42:54 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=736bd39f-f225-30da-b126-c02deb08e181
2025-08-05 10:42:55 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-05 10:42:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2531 ms
2025-08-05 10:42:56 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-05 10:42:56 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-05 10:42:57 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-05 10:42:57 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-05 10:42:58 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-05 10:42:58 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-05 10:42:58 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-05 10:42:58 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-05 10:42:58 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-05 10:42:59 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-05 10:42:59 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.336 seconds (process running for 8.158)
2025-08-05 10:42:59 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-05 10:42:59 [RMI TCP Connection(3)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 10:42:59 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-05 10:42:59 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-08-05 10:42:59 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-05 10:43:03 [XNIO-1 task-5] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1197 ms
2025-08-05 10:43:04 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@ed50c5c
2025-08-05 10:43:04 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-05 10:43:13 [XNIO-1 task-5] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703980800000
2025-08-05 10:43:13 [XNIO-1 task-5] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 10:45:36 [XNIO-1 task-5] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703980800000
2025-08-05 10:45:36 [XNIO-1 task-5] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 11:25:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 11:25:54 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-05 11:25:54 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-05 11:25:54 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05 11:25:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-05 11:25:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-05 11:28:45 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 11:28:45 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 31524 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-05 11:28:45 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-05 11:28:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 11:28:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 11:28:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-08-05 11:28:47 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=5b34fd8f-ab92-3b45-a391-12edc91f865b
2025-08-05 11:28:48 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-05 11:28:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2490 ms
2025-08-05 11:28:49 [main] INFO  c.z.i.p.i.a.f.GpaProviderFactory - 开始初始化GPA数据获取策略工厂
2025-08-05 11:28:49 [main] INFO  c.z.i.p.i.a.f.GpaProviderFactory - GPA数据获取策略工厂初始化完成，共注册3个策略
2025-08-05 11:28:49 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-05 11:28:50 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-05 11:28:50 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-05 11:28:50 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-05 11:28:51 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-05 11:28:51 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-05 11:28:51 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-05 11:28:51 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-05 11:28:51 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-05 11:28:51 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-05 11:28:52 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.889 seconds (process running for 7.679)
2025-08-05 11:28:52 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-05 11:28:52 [RMI TCP Connection(3)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 11:28:52 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-05 11:28:52 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-05 11:28:52 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-05 11:28:56 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1536 ms
2025-08-05 11:28:57 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@668397b6
2025-08-05 11:28:57 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-05 11:29:05 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703980800000
2025-08-05 11:29:05 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2023-2024-1
2025-08-05 11:29:05 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - GPA分析数据获取完成：预警学生1条，榜样学生0条，相似学生0条
2025-08-05 11:29:05 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 11:29:59 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703980800000
2025-08-05 11:29:59 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2023-2024-1
2025-08-05 11:29:59 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - GPA分析数据获取完成：预警学生1条，榜样学生0条，相似学生0条
2025-08-05 11:29:59 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 11:30:09 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 开始获取学生202310002的GPA分析数据，时间戳：1703980800000
2025-08-05 11:30:09 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - 开始获取学生202310002的GPA分析数据，当前学期：2023-2024-1
2025-08-05 11:30:09 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - GPA分析数据获取完成：预警学生1条，榜样学生0条，相似学生0条
2025-08-05 11:30:09 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 学生202310002的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 11:30:14 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 开始获取学生202310003的GPA分析数据，时间戳：1703980800000
2025-08-05 11:30:14 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - 开始获取学生202310003的GPA分析数据，当前学期：2023-2024-1
2025-08-05 11:30:14 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - GPA分析数据获取完成：预警学生1条，榜样学生0条，相似学生0条
2025-08-05 11:30:14 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 学生202310003的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 11:30:19 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 开始获取学生202310004的GPA分析数据，时间戳：1703980800000
2025-08-05 11:30:19 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - 开始获取学生202310004的GPA分析数据，当前学期：2023-2024-1
2025-08-05 11:30:19 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - GPA分析数据获取完成：预警学生1条，榜样学生0条，相似学生0条
2025-08-05 11:30:19 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 学生202310004的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 11:30:25 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 开始获取学生202310005的GPA分析数据，时间戳：1703980800000
2025-08-05 11:30:25 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - 开始获取学生202310005的GPA分析数据，当前学期：2023-2024-1
2025-08-05 11:30:25 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - GPA分析数据获取完成：预警学生1条，榜样学生0条，相似学生0条
2025-08-05 11:30:25 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 学生202310005的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 11:30:43 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703980800000
2025-08-05 11:30:43 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2023-2024-1
2025-08-05 11:30:43 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - GPA分析数据获取完成：预警学生1条，榜样学生0条，相似学生0条
2025-08-05 11:30:43 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 14:23:21 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 14:23:21 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-05 14:23:21 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-05 14:23:21 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05 14:23:21 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-05 14:23:21 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-05 14:23:27 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 14:23:27 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 48044 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-05 14:23:27 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-05 14:23:28 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 14:23:28 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 14:23:28 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-08-05 14:23:28 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=91eacb81-50be-3dfb-bf61-57552da6157a
2025-08-05 14:23:29 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-05 14:23:29 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2329 ms
2025-08-05 14:23:30 [main] INFO  c.z.i.p.i.a.f.GpaProviderFactory - 开始初始化GPA数据获取策略工厂
2025-08-05 14:23:30 [main] INFO  c.z.i.p.i.a.f.GpaProviderFactory - GPA数据获取策略工厂初始化完成，共注册3个策略
2025-08-05 14:23:31 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-05 14:23:34 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-05 14:23:36 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-05 14:23:36 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-05 14:23:37 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-05 14:23:37 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-05 14:23:37 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-05 14:23:37 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-05 14:23:37 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-05 14:23:38 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-05 14:23:38 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 11.323 seconds (process running for 12.177)
2025-08-05 14:23:38 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-05 14:23:38 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 14:23:38 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-05 14:23:38 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-05 14:23:38 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-05 14:23:43 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5b33a2ae
2025-08-05 14:23:43 [RMI TCP Connection(3)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-05 14:23:44 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1119 ms
2025-08-05 14:23:52 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703980800000
2025-08-05 14:23:52 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2023-2024-1
2025-08-05 14:23:52 [XNIO-1 task-2] INFO  c.z.i.p.i.a.GpaAnalysisProvider - GPA分析数据获取完成：预警学生1条，榜样学生3条，相似学生3条
2025-08-05 14:23:52 [XNIO-1 task-2] INFO  c.z.i.s.impl.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 14:29:45 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 14:29:45 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-05 14:29:45 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-05 14:29:45 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05 14:29:45 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-05 14:29:45 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-05 14:29:54 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 14:29:54 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 50200 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-05 14:29:54 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-05 14:29:56 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 14:29:56 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 14:29:56 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 39 ms. Found 0 Redis repository interfaces.
2025-08-05 14:29:56 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=91eacb81-50be-3dfb-bf61-57552da6157a
2025-08-05 14:29:57 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-05 14:29:57 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2396 ms
2025-08-05 14:29:58 [main] INFO  c.z.i.p.i.g.f.GpaProviderFactory - 开始初始化GPA数据获取策略工厂
2025-08-05 14:29:58 [main] INFO  c.z.i.p.i.g.f.GpaProviderFactory - GPA数据获取策略工厂初始化完成，共注册3个策略
2025-08-05 14:29:58 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-05 14:29:58 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-05 14:29:59 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-05 14:29:59 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-05 14:30:00 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-05 14:30:00 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-05 14:30:00 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-05 14:30:00 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-05 14:30:00 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-05 14:30:00 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-05 14:30:01 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.86 seconds (process running for 7.602)
2025-08-05 14:30:01 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-05 14:30:01 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-05 14:30:01 [RMI TCP Connection(4)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 14:30:01 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-05 14:30:01 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-05 14:30:06 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5e37aa5a
2025-08-05 14:30:06 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-05 14:30:37 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 开始获取学生202310002的GPA分析数据，时间戳：1703980800000
2025-08-05 14:30:37 [XNIO-1 task-2] INFO  c.z.iai.provider.GpaAnalysisProvider - 开始获取学生202310002的GPA分析数据，当前学期：2023-2024-1
2025-08-05 14:30:39 [XNIO-1 task-2] INFO  c.z.iai.provider.GpaAnalysisProvider - GPA分析数据获取完成：预警学生1条，榜样学生3条，相似学生3条
2025-08-05 14:30:39 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 学生202310002的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 14:30:52 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 开始获取学生202310003的GPA分析数据，时间戳：1703980800000
2025-08-05 14:30:52 [XNIO-1 task-2] INFO  c.z.iai.provider.GpaAnalysisProvider - 开始获取学生202310003的GPA分析数据，当前学期：2023-2024-1
2025-08-05 14:30:52 [XNIO-1 task-2] INFO  c.z.iai.provider.GpaAnalysisProvider - GPA分析数据获取完成：预警学生1条，榜样学生3条，相似学生3条
2025-08-05 14:30:52 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 学生202310003的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 15:13:49 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 15:13:49 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-05 15:13:49 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-05 15:13:49 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05 15:13:49 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-05 15:13:49 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-05 15:44:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 15:44:19 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 38316 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-05 15:44:19 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-05 15:44:21 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 15:44:21 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 15:44:21 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45 ms. Found 0 Redis repository interfaces.
2025-08-05 15:44:21 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07a29cd9-918c-312b-aa8b-b2dc93b1ba3e
2025-08-05 15:44:22 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-05 15:44:22 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2542 ms
2025-08-05 15:44:23 [main] INFO  c.z.i.s.g.factory.GpaProviderFactory - 开始初始化GPA数据获取策略工厂
2025-08-05 15:44:23 [main] INFO  c.z.i.s.g.factory.GpaProviderFactory - GPA数据获取策略工厂初始化完成，共注册3个策略
2025-08-05 15:44:23 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-05 15:44:24 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-05 15:44:24 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-05 15:44:24 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-05 15:44:26 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-05 15:44:26 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-05 15:44:26 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-05 15:44:26 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-05 15:44:26 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-05 15:44:26 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-05 15:44:26 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.209 seconds (process running for 8.078)
2025-08-05 15:44:26 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-05 15:44:27 [RMI TCP Connection(4)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 15:44:27 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-05 15:44:27 [RMI TCP Connection(4)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-08-05 15:44:27 [RMI TCP Connection(5)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-05 15:44:31 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1138 ms
2025-08-05 15:44:31 [RMI TCP Connection(5)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@17bf98ea
2025-08-05 15:44:31 [RMI TCP Connection(5)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-05 15:45:16 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703980800000
2025-08-05 15:45:16 [XNIO-1 task-2] INFO  c.z.iai.strategy.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2023-2024-1
2025-08-05 15:45:16 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 15:48:53 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703980000000
2025-08-05 15:48:53 [XNIO-1 task-2] INFO  c.z.iai.strategy.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2023-2024-1
2025-08-05 15:48:53 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 15:49:09 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703880000000
2025-08-05 15:49:09 [XNIO-1 task-2] INFO  c.z.iai.strategy.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2023-2024-1
2025-08-05 15:49:09 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 15:49:21 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1603880000000
2025-08-05 15:49:21 [XNIO-1 task-2] INFO  c.z.iai.strategy.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2020-2021-1
2025-08-05 15:49:21 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2020-2021-1
2025-08-05 15:50:18 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1903880000000
2025-08-05 15:50:18 [XNIO-1 task-2] INFO  c.z.iai.strategy.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2029-2030-2
2025-08-05 15:50:18 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2029-2030-2
2025-08-05 15:50:25 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1803880000000
2025-08-05 15:50:25 [XNIO-1 task-2] INFO  c.z.iai.strategy.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2026-2027-2
2025-08-05 15:50:25 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2026-2027-2
2025-08-05 15:50:31 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703880000000
2025-08-05 15:50:31 [XNIO-1 task-2] INFO  c.z.iai.strategy.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2023-2024-1
2025-08-05 15:50:31 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 16:44:59 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 开始获取学生202310001的GPA分析数据，时间戳：1703880000000
2025-08-05 16:44:59 [XNIO-1 task-2] INFO  c.z.iai.strategy.GpaAnalysisProvider - 开始获取学生202310001的GPA分析数据，当前学期：2023-2024-1
2025-08-05 16:44:59 [XNIO-1 task-2] INFO  c.z.i.s.i.a.GpaAnalysisServiceImpl - 学生202310001的GPA分析数据获取成功，当前学期：2023-2024-1
2025-08-05 17:28:46 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 17:28:46 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-05 17:28:46 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-05 17:28:46 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05 17:28:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-05 17:28:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-05 17:29:05 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 17:29:05 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 52156 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-05 17:29:05 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-05 17:29:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 17:29:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 17:29:07 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-08-05 17:29:07 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07a29cd9-918c-312b-aa8b-b2dc93b1ba3e
2025-08-05 17:29:08 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-05 17:29:08 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2524 ms
2025-08-05 17:29:09 [main] INFO  c.z.i.s.g.factory.GpaProviderFactory - 开始初始化GPA数据获取策略工厂
2025-08-05 17:29:09 [main] INFO  c.z.i.s.g.factory.GpaProviderFactory - GPA数据获取策略工厂初始化完成，共注册3个策略
2025-08-05 17:29:09 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-05 17:29:10 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-05 17:29:10 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-05 17:29:10 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-05 17:29:11 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-05 17:29:11 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-05 17:29:11 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-05 17:29:11 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-05 17:29:12 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-05 17:29:12 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-05 17:29:12 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.082 seconds (process running for 8.274)
2025-08-05 17:29:12 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-05 17:29:12 [RMI TCP Connection(3)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 17:29:12 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-05 17:29:12 [RMI TCP Connection(3)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-05 17:29:12 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-05 17:29:17 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@6cbfa37a
2025-08-05 17:29:17 [RMI TCP Connection(4)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-05 17:37:19 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 17:37:19 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-05 17:37:19 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-05 17:37:19 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05 17:37:19 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-05 17:37:19 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-05 17:37:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 17:37:22 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 21924 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-05 17:37:22 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-05 17:37:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 17:37:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 17:37:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-08-05 17:37:23 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07a29cd9-918c-312b-aa8b-b2dc93b1ba3e
2025-08-05 17:37:24 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-05 17:37:24 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2464 ms
2025-08-05 17:37:25 [main] INFO  c.z.i.s.g.factory.GpaProviderFactory - 开始初始化GPA数据获取策略工厂
2025-08-05 17:37:25 [main] INFO  c.z.i.s.g.factory.GpaProviderFactory - GPA数据获取策略工厂初始化完成，共注册3个策略
2025-08-05 17:37:26 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-05 17:37:26 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-05 17:37:26 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-05 17:37:26 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-05 17:37:28 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-05 17:37:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-05 17:37:28 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-05 17:37:28 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-05 17:37:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-05 17:37:28 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-05 17:37:28 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.955 seconds (process running for 7.938)
2025-08-05 17:37:28 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-05 17:37:28 [RMI TCP Connection(2)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 17:37:28 [RMI TCP Connection(2)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-05 17:37:28 [RMI TCP Connection(2)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-05 17:37:28 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-05 17:37:32 [XNIO-1 task-2] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1159 ms
2025-08-05 17:37:33 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5a1fd93b
2025-08-05 17:37:33 [RMI TCP Connection(1)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-05 17:44:56 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 17:44:56 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-05 17:44:56 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-05 17:44:56 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05 17:44:56 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-05 17:44:56 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-05 17:44:59 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 17:44:59 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 32432 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-08-05 17:44:59 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-08-05 17:45:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-05 17:45:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 17:45:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-08-05 17:45:01 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=07a29cd9-918c-312b-aa8b-b2dc93b1ba3e
2025-08-05 17:45:01 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-05 17:45:01 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2403 ms
2025-08-05 17:45:03 [main] INFO  c.z.i.s.g.factory.GpaProviderFactory - 开始初始化GPA数据获取策略工厂
2025-08-05 17:45:03 [main] INFO  c.z.i.s.g.factory.GpaProviderFactory - GPA数据获取策略工厂初始化完成，共注册3个策略
2025-08-05 17:45:03 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-05 17:45:03 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-08-05 17:45:04 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-08-05 17:45:04 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-08-05 17:45:05 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-08-05 17:45:05 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-05 17:45:05 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-05 17:45:05 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-05 17:45:05 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-05 17:45:06 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-05 17:45:06 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.141 seconds (process running for 8.193)
2025-08-05 17:45:06 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8080/swagger-ui/index.html
2025-08-05 17:45:06 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 17:45:06 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-05 17:45:06 [RMI TCP Connection(1)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-08-05 17:45:06 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-05 17:45:11 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@411d91bd
2025-08-05 17:45:11 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-06 09:34:02 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-06 09:34:02 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-06 09:34:02 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-06 09:34:02 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-06 09:34:02 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-06 09:34:02 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
