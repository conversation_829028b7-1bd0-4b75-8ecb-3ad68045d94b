#!/bin/bash

# 最终版GPA分析功能测试脚本
# 使用正确的学号格式，确保学期转换正确

BASE_URL="http://localhost:8080"
API_PATH="/api/v1/warnings/academic"

echo "🎯 GPA分析功能最终测试"
echo "======================"
echo ""

# 测试函数
test_gpa_analysis() {
    local student_id=$1
    local scenario=$2
    local timestamp=$3
    local expected_warning_count=$4
    local expected_exemplary_count=$5
    local expected_similar_count=$6
    
    echo "📋 测试场景: $scenario"
    echo "👤 学生学号: $student_id"
    
    local url="${BASE_URL}${API_PATH}/${student_id}/gpa-analysis"
    if [ ! -z "$timestamp" ]; then
        url="${url}?timestamp=${timestamp}"
    fi
    
    echo "🌐 请求URL: $url"
    echo ""
    
    # 发送请求
    local response=$(curl -s "$url")
    
    # 检查响应
    if echo "$response" | grep -q '"code":200'; then
        echo "✅ 接口调用成功"
        
        # 提取数据
        local warning_count=$(echo "$response" | jq '.data.warningStudentGpa | length' 2>/dev/null || echo "0")
        local exemplary_count=$(echo "$response" | jq '.data.exemplaryStudentGpa | length' 2>/dev/null || echo "0")
        local similar_count=$(echo "$response" | jq '.data.similarStudentGpa | length' 2>/dev/null || echo "0")
        
        echo "📊 数据统计:"
        echo "   预警学生: $warning_count 条记录 (期望: $expected_warning_count)"
        echo "   榜样学生: $exemplary_count 条记录 (期望: $expected_exemplary_count)"
        echo "   相似学生: $similar_count 条记录 (期望: $expected_similar_count)"
        
        # 提取学期信息
        echo ""
        echo "📅 学期信息:"
        echo "   预警学生学期:"
        echo "$response" | jq -r '.data.warningStudentGpa[]?.semester' 2>/dev/null | sed 's/^/     /'
        echo "   榜样学生学期:"
        echo "$response" | jq -r '.data.exemplaryStudentGpa[]?.semester' 2>/dev/null | sed 's/^/     /'
        echo "   相似学生学期:"
        echo "$response" | jq -r '.data.similarStudentGpa[]?.semester' 2>/dev/null | sed 's/^/     /'
        
        # 验证数据
        local validation_passed=true
        local validation_messages=()
        
        # 检查数据量
        if [ "$warning_count" -ne "$expected_warning_count" ]; then
            validation_messages+=("预警学生数据量不符合预期")
            validation_passed=false
        fi
        
        if [ "$exemplary_count" -ne "$expected_exemplary_count" ]; then
            validation_messages+=("榜样学生数据量不符合预期")
            validation_passed=false
        fi
        
        if [ "$similar_count" -ne "$expected_similar_count" ]; then
            validation_messages+=("相似学生数据量不符合预期")
            validation_passed=false
        fi
        
        # 检查学期格式
        local semester_check=$(echo "$response" | jq -r '.data.exemplaryStudentGpa[]?.semester' 2>/dev/null | head -1)
        if [[ "$semester_check" == *"大-"* ]]; then
            validation_messages+=("学期格式错误，出现负数年级")
            validation_passed=false
        fi
        
        echo ""
        if [ "$validation_passed" = true ]; then
            echo "✅ 数据验证通过"
        else
            echo "❌ 数据验证失败:"
            for msg in "${validation_messages[@]}"; do
                echo "    - $msg"
            done
        fi
    else
        echo "❌ 接口调用失败"
        echo "📄 响应内容: $response"
    fi
    
    echo ""
    echo "----------------------------------------"
    echo ""
}

# 执行测试用例
echo "开始执行测试用例..."
echo ""

# 测试用例1：预警学生当前大一下
# 预期：预警学生2条(大一上+大一下)，榜样/相似学生7条(大一下到大四下)
test_gpa_analysis "201910001" "预警学生当前大一下（2019-2020-2）" "1577836800000" 2 7 7

# 测试用例2：预警学生当前大二上
# 预期：预警学生3条(大一上+大一下+大二上)，榜样/相似学生6条(大二上到大四下)
test_gpa_analysis "201910004" "预警学生当前大二上（2020-2021-1）" "1598918400000" 3 6 6

# 测试用例3：预警学生当前大三上
# 预期：预警学生5条(大一上到大三上)，榜样/相似学生4条(大三上到大四下)
test_gpa_analysis "201910007" "预警学生当前大三上（2021-2022-1）" "1630454400000" 5 4 4

echo "🎉 测试完成！"
echo ""
echo "💡 关键修复点："
echo "1. ✅ 使用正确的学号格式（2019开头），确保学期转换正确"
echo "2. ✅ 修复过滤逻辑，从'当前学期之后'改为'当前学期及之后'"
echo "3. ✅ 使用正确的数据库表名和字段名"
echo ""
echo "🔍 验证要点："
echo "1. 学期显示应为'大一上'、'大一下'等，不应出现负数"
echo "2. 榜样/相似学生应返回从当前学期到大四下的完整数据"
echo "3. 预警学生应返回从大一上到当前学期的数据"
echo ""
echo "🛠️ 如果仍有问题，请检查："
echo "1. 数据库中是否已插入最新的测试数据"
echo "2. 应用是否已重启以加载代码修改"
echo "3. 时间戳计算是否正确对应学期"
