#!/bin/bash

# 简化的GPA分析功能测试脚本
# 用于快速测试修复后的接口

BASE_URL="http://localhost:8080"
API_PATH="/api/v1/warnings/academic"

echo "GPA分析功能测试"
echo "================"

# 测试函数
test_api() {
    local student_id=$1
    local scenario=$2
    local timestamp=$3
    
    echo ""
    echo "测试场景: $scenario"
    echo "学生学号: $student_id"
    
    local url="${BASE_URL}${API_PATH}/${student_id}/gpa-analysis"
    if [ ! -z "$timestamp" ]; then
        url="${url}?timestamp=${timestamp}"
    fi
    
    echo "请求URL: $url"
    
    # 发送请求并显示结果
    curl -s "$url" | jq '.' || echo "请求失败或响应格式错误"
}

# 执行测试用例
echo "开始测试..."

# 测试用例1：预警学生当前大一下
test_api "TEST001" "预警学生当前大一下（2019-2020-2）" "1577836800000"

# 测试用例2：预警学生当前大二上
test_api "TEST002" "预警学生当前大二上（2020-2021-1）" "1598918400000"

# 测试用例3：预警学生当前大三上
test_api "TEST003" "预警学生当前大三上（2021-2022-1）" "1630454400000"

# 测试用例4：预警学生当前大四上
test_api "TEST004" "预警学生当前大四上（2022-2023-1）" "1661990400000"

# 测试用例5：预警学生当前大一上
test_api "TEST005" "预警学生当前大一上（2019-2020-1）" "1567267200000"

echo ""
echo "测试完成！"
echo ""
echo "验证要点："
echo "1. 检查HTTP状态码是否为200"
echo "2. 检查业务状态码是否为200"
echo "3. 检查榜样学生和相似学生是否返回从当前学期到大四下的完整数据"
echo "4. 检查预警学生是否返回从大一上到当前学期的数据"
