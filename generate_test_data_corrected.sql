-- GPA分析功能测试数据生成脚本（最终修正版）
-- 生成5组相关联的测试数据，每组包含预警学生、榜样学生、相似学生
-- 字段说明：WYBS=主键, XH=学号, XNXQ=学年学期, GPA=绩点, JQPJF=加权平均分, SSPJF=算术平均分
-- ZXF=总学分, TGZXF=通过总学分, WTCZXF=未通过总学分, SFCYPM=是否参与排名

-- 清理现有测试数据（可选）
-- DELETE FROM T_GXXS_BKSXQJDXX WHERE XH LIKE 'TEST%';
-- DELETE FROM T_GXYS_YJXX WHERE XH LIKE 'TEST%';

-- =====================================================
-- 测试组1：预警学生当前大一下（2019-2020-2）
-- =====================================================

-- 预警学生：TEST001（2019年入学）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST001_001', 'TEST001', '2019-2020-1', 3.2, 92.50, 91.80, 20.0, 20.0, 0.0, '1'),
('TEST001_002', 'TEST001', '2019-2020-2', 3.1, 91.20, 90.50, 18.5, 18.5, 0.0, '1');

-- 榜样学生：TEST001_EX（2019年入学，对应榜样）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST001_EX_001', 'TEST001_EX', '2019-2020-1', 3.8, 95.20, 94.80, 20.0, 20.0, 0.0, '1'),
('TEST001_EX_002', 'TEST001_EX', '2019-2020-2', 3.9, 96.10, 95.60, 18.5, 18.5, 0.0, '1'),
('TEST001_EX_003', 'TEST001_EX', '2020-2021-1', 4.0, 97.30, 96.80, 19.0, 19.0, 0.0, '1'),
('TEST001_EX_004', 'TEST001_EX', '2020-2021-2', 3.9, 96.50, 96.20, 18.0, 18.0, 0.0, '1'),
('TEST001_EX_005', 'TEST001_EX', '2021-2022-1', 4.0, 97.80, 97.40, 19.5, 19.5, 0.0, '1'),
('TEST001_EX_006', 'TEST001_EX', '2021-2022-2', 4.0, 98.10, 97.90, 18.0, 18.0, 0.0, '1'),
('TEST001_EX_007', 'TEST001_EX', '2022-2023-1', 3.9, 97.20, 96.90, 17.5, 17.5, 0.0, '1'),
('TEST001_EX_008', 'TEST001_EX', '2022-2023-2', 4.0, 98.50, 98.20, 16.0, 16.0, 0.0, '1');

-- 相似学生：TEST001_SI（2019年入学，对应相似）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST001_SI_001', 'TEST001_SI', '2019-2020-1', 3.3, 92.80, 92.10, 20.0, 20.0, 0.0, '1'),
('TEST001_SI_002', 'TEST001_SI', '2019-2020-2', 3.4, 93.40, 92.90, 18.5, 18.5, 0.0, '1'),
('TEST001_SI_003', 'TEST001_SI', '2020-2021-1', 3.6, 94.70, 94.10, 19.0, 19.0, 0.0, '1'),
('TEST001_SI_004', 'TEST001_SI', '2020-2021-2', 3.7, 95.10, 94.50, 18.0, 18.0, 0.0, '1'),
('TEST001_SI_005', 'TEST001_SI', '2021-2022-1', 3.8, 95.90, 95.30, 19.5, 19.5, 0.0, '1'),
('TEST001_SI_006', 'TEST001_SI', '2021-2022-2', 3.8, 96.20, 95.80, 18.0, 18.0, 0.0, '1'),
('TEST001_SI_007', 'TEST001_SI', '2022-2023-1', 3.7, 95.50, 95.10, 17.5, 17.5, 0.0, '1'),
('TEST001_SI_008', 'TEST001_SI', '2022-2023-2', 3.9, 96.80, 96.40, 16.0, 16.0, 0.0, '1');

-- 预警信息
INSERT INTO T_GXYS_YJXX (XH, YJLX_M, YJDJ_M, YJZT_M, MBYXSH, XSYXSH) VALUES
('TEST001', '01', '2', '01', 'TEST001_EX', 'TEST001_SI');

-- =====================================================
-- 测试组2：预警学生当前大二上（2020-2021-1）
-- =====================================================

-- 预警学生：TEST002（2019年入学）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST002_001', 'TEST002', '2019-2020-1', 3.0, 90.20, 89.80, 20.0, 20.0, 0.0, '1'),
('TEST002_002', 'TEST002', '2019-2020-2', 2.9, 89.50, 89.10, 18.5, 18.5, 0.0, '1'),
('TEST002_003', 'TEST002', '2020-2021-1', 3.1, 91.30, 90.90, 19.0, 19.0, 0.0, '1');

-- 榜样学生：TEST002_EX
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST002_EX_001', 'TEST002_EX', '2019-2020-1', 3.9, 96.20, 95.80, 20.0, 20.0, 0.0, '1'),
('TEST002_EX_002', 'TEST002_EX', '2019-2020-2', 4.0, 97.10, 96.60, 18.5, 18.5, 0.0, '1'),
('TEST002_EX_003', 'TEST002_EX', '2020-2021-1', 4.0, 97.80, 97.40, 19.0, 19.0, 0.0, '1'),
('TEST002_EX_004', 'TEST002_EX', '2020-2021-2', 3.9, 96.90, 96.50, 18.0, 18.0, 0.0, '1'),
('TEST002_EX_005', 'TEST002_EX', '2021-2022-1', 4.0, 98.20, 97.80, 19.5, 19.5, 0.0, '1'),
('TEST002_EX_006', 'TEST002_EX', '2021-2022-2', 4.0, 98.50, 98.10, 18.0, 18.0, 0.0, '1'),
('TEST002_EX_007', 'TEST002_EX', '2022-2023-1', 3.9, 97.60, 97.20, 17.5, 17.5, 0.0, '1'),
('TEST002_EX_008', 'TEST002_EX', '2022-2023-2', 4.0, 98.80, 98.40, 16.0, 16.0, 0.0, '1');

-- 相似学生：TEST002_SI
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST002_SI_001', 'TEST002_SI', '2019-2020-1', 3.1, 91.20, 90.80, 20.0, 20.0, 0.0, '1'),
('TEST002_SI_002', 'TEST002_SI', '2019-2020-2', 3.2, 92.40, 91.90, 18.5, 18.5, 0.0, '1'),
('TEST002_SI_003', 'TEST002_SI', '2020-2021-1', 3.5, 94.30, 93.80, 19.0, 19.0, 0.0, '1'),
('TEST002_SI_004', 'TEST002_SI', '2020-2021-2', 3.6, 94.80, 94.30, 18.0, 18.0, 0.0, '1'),
('TEST002_SI_005', 'TEST002_SI', '2021-2022-1', 3.7, 95.60, 95.10, 19.5, 19.5, 0.0, '1'),
('TEST002_SI_006', 'TEST002_SI', '2021-2022-2', 3.8, 96.10, 95.60, 18.0, 18.0, 0.0, '1'),
('TEST002_SI_007', 'TEST002_SI', '2022-2023-1', 3.7, 95.40, 94.90, 17.5, 17.5, 0.0, '1'),
('TEST002_SI_008', 'TEST002_SI', '2022-2023-2', 3.8, 96.50, 96.00, 16.0, 16.0, 0.0, '1');

-- 预警信息
INSERT INTO T_GXYS_YJXX (XH, YJLX_M, YJDJ_M, YJZT_M, MBYXSH, XSYXSH) VALUES
('TEST002', '01', '2', '01', 'TEST002_EX', 'TEST002_SI');

-- =====================================================
-- 测试组3：预警学生当前大三上（2021-2022-1）
-- =====================================================

-- 预警学生：TEST003（2019年入学）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST003_001', 'TEST003', '2019-2020-1', 2.8, 88.20, 87.80, 20.0, 20.0, 0.0, '1'),
('TEST003_002', 'TEST003', '2019-2020-2', 2.9, 89.10, 88.60, 18.5, 18.5, 0.0, '1'),
('TEST003_003', 'TEST003', '2020-2021-1', 3.0, 90.30, 89.90, 19.0, 19.0, 0.0, '1'),
('TEST003_004', 'TEST003', '2020-2021-2', 3.1, 91.20, 90.80, 18.0, 18.0, 0.0, '1'),
('TEST003_005', 'TEST003', '2021-2022-1', 3.2, 92.10, 91.70, 19.5, 19.5, 0.0, '1');

-- 榜样学生：TEST003_EX
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST003_EX_001', 'TEST003_EX', '2019-2020-1', 3.7, 94.20, 93.80, 20.0, 20.0, 0.0, '1'),
('TEST003_EX_002', 'TEST003_EX', '2019-2020-2', 3.8, 95.10, 94.60, 18.5, 18.5, 0.0, '1'),
('TEST003_EX_003', 'TEST003_EX', '2020-2021-1', 3.9, 96.30, 95.90, 19.0, 19.0, 0.0, '1'),
('TEST003_EX_004', 'TEST003_EX', '2020-2021-2', 4.0, 97.20, 96.80, 18.0, 18.0, 0.0, '1'),
('TEST003_EX_005', 'TEST003_EX', '2021-2022-1', 4.0, 98.10, 97.70, 19.5, 19.5, 0.0, '1'),
('TEST003_EX_006', 'TEST003_EX', '2021-2022-2', 3.9, 97.50, 97.10, 18.0, 18.0, 0.0, '1'),
('TEST003_EX_007', 'TEST003_EX', '2022-2023-1', 4.0, 98.40, 98.00, 17.5, 17.5, 0.0, '1'),
('TEST003_EX_008', 'TEST003_EX', '2022-2023-2', 4.0, 98.90, 98.50, 16.0, 16.0, 0.0, '1');

-- 相似学生：TEST003_SI
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST003_SI_001', 'TEST003_SI', '2019-2020-1', 2.9, 89.20, 88.80, 20.0, 20.0, 0.0, '1'),
('TEST003_SI_002', 'TEST003_SI', '2019-2020-2', 3.0, 90.40, 89.90, 18.5, 18.5, 0.0, '1'),
('TEST003_SI_003', 'TEST003_SI', '2020-2021-1', 3.3, 92.30, 91.80, 19.0, 19.0, 0.0, '1'),
('TEST003_SI_004', 'TEST003_SI', '2020-2021-2', 3.4, 93.20, 92.70, 18.0, 18.0, 0.0, '1'),
('TEST003_SI_005', 'TEST003_SI', '2021-2022-1', 3.6, 94.60, 94.10, 19.5, 19.5, 0.0, '1'),
('TEST003_SI_006', 'TEST003_SI', '2021-2022-2', 3.7, 95.10, 94.60, 18.0, 18.0, 0.0, '1'),
('TEST003_SI_007', 'TEST003_SI', '2022-2023-1', 3.6, 94.40, 93.90, 17.5, 17.5, 0.0, '1'),
('TEST003_SI_008', 'TEST003_SI', '2022-2023-2', 3.8, 95.80, 95.30, 16.0, 16.0, 0.0, '1');

-- 预警信息
INSERT INTO T_GXYS_YJXX (XH, YJLX_M, YJDJ_M, YJZT_M, MBYXSH, XSYXSH) VALUES
('TEST003', '01', '2', '01', 'TEST003_EX', 'TEST003_SI');

-- =====================================================
-- 测试组4：预警学生当前大四上（2022-2023-1）
-- =====================================================

-- 预警学生：TEST004（2019年入学）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST004_001', 'TEST004', '2019-2020-1', 2.7, 87.20, 86.80, 20.0, 20.0, 0.0, '1'),
('TEST004_002', 'TEST004', '2019-2020-2', 2.8, 88.10, 87.60, 18.5, 18.5, 0.0, '1'),
('TEST004_003', 'TEST004', '2020-2021-1', 2.9, 89.30, 88.90, 19.0, 19.0, 0.0, '1'),
('TEST004_004', 'TEST004', '2020-2021-2', 3.0, 90.20, 89.80, 18.0, 18.0, 0.0, '1'),
('TEST004_005', 'TEST004', '2021-2022-1', 3.1, 91.10, 90.70, 19.5, 19.5, 0.0, '1'),
('TEST004_006', 'TEST004', '2021-2022-2', 3.2, 92.00, 91.60, 18.0, 18.0, 0.0, '1'),
('TEST004_007', 'TEST004', '2022-2023-1', 3.3, 92.90, 92.50, 17.5, 17.5, 0.0, '1');

-- 榜样学生：TEST004_EX
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST004_EX_001', 'TEST004_EX', '2019-2020-1', 3.6, 93.20, 92.80, 20.0, 20.0, 0.0, '1'),
('TEST004_EX_002', 'TEST004_EX', '2019-2020-2', 3.7, 94.10, 93.60, 18.5, 18.5, 0.0, '1'),
('TEST004_EX_003', 'TEST004_EX', '2020-2021-1', 3.8, 95.30, 94.90, 19.0, 19.0, 0.0, '1'),
('TEST004_EX_004', 'TEST004_EX', '2020-2021-2', 3.9, 96.20, 95.80, 18.0, 18.0, 0.0, '1'),
('TEST004_EX_005', 'TEST004_EX', '2021-2022-1', 4.0, 97.10, 96.70, 19.5, 19.5, 0.0, '1'),
('TEST004_EX_006', 'TEST004_EX', '2021-2022-2', 4.0, 97.50, 97.10, 18.0, 18.0, 0.0, '1'),
('TEST004_EX_007', 'TEST004_EX', '2022-2023-1', 4.0, 98.40, 98.00, 17.5, 17.5, 0.0, '1'),
('TEST004_EX_008', 'TEST004_EX', '2022-2023-2', 4.0, 98.90, 98.50, 16.0, 16.0, 0.0, '1');

-- 相似学生：TEST004_SI
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST004_SI_001', 'TEST004_SI', '2019-2020-1', 2.8, 88.20, 87.80, 20.0, 20.0, 0.0, '1'),
('TEST004_SI_002', 'TEST004_SI', '2019-2020-2', 2.9, 89.40, 88.90, 18.5, 18.5, 0.0, '1'),
('TEST004_SI_003', 'TEST004_SI', '2020-2021-1', 3.2, 91.30, 90.80, 19.0, 19.0, 0.0, '1'),
('TEST004_SI_004', 'TEST004_SI', '2020-2021-2', 3.3, 92.20, 91.70, 18.0, 18.0, 0.0, '1'),
('TEST004_SI_005', 'TEST004_SI', '2021-2022-1', 3.5, 93.60, 93.10, 19.5, 19.5, 0.0, '1'),
('TEST004_SI_006', 'TEST004_SI', '2021-2022-2', 3.6, 94.10, 93.60, 18.0, 18.0, 0.0, '1'),
('TEST004_SI_007', 'TEST004_SI', '2022-2023-1', 3.7, 94.90, 94.40, 17.5, 17.5, 0.0, '1'),
('TEST004_SI_008', 'TEST004_SI', '2022-2023-2', 3.8, 95.80, 95.30, 16.0, 16.0, 0.0, '1');

-- 预警信息
INSERT INTO T_GXYS_YJXX (XH, YJLX_M, YJDJ_M, YJZT_M, MBYXSH, XSYXSH) VALUES
('TEST004', '01', '2', '01', 'TEST004_EX', 'TEST004_SI');

-- =====================================================
-- 测试组5：预警学生当前大一上（2019-2020-1）
-- =====================================================

-- 预警学生：TEST005（2019年入学）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST005_001', 'TEST005', '2019-2020-1', 3.0, 90.50, 90.10, 20.0, 20.0, 0.0, '1');

-- 榜样学生：TEST005_EX
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST005_EX_001', 'TEST005_EX', '2019-2020-1', 3.9, 96.50, 96.10, 20.0, 20.0, 0.0, '1'),
('TEST005_EX_002', 'TEST005_EX', '2019-2020-2', 4.0, 97.20, 96.80, 18.5, 18.5, 0.0, '1'),
('TEST005_EX_003', 'TEST005_EX', '2020-2021-1', 4.0, 97.80, 97.40, 19.0, 19.0, 0.0, '1'),
('TEST005_EX_004', 'TEST005_EX', '2020-2021-2', 3.9, 96.90, 96.50, 18.0, 18.0, 0.0, '1'),
('TEST005_EX_005', 'TEST005_EX', '2021-2022-1', 4.0, 98.20, 97.80, 19.5, 19.5, 0.0, '1'),
('TEST005_EX_006', 'TEST005_EX', '2021-2022-2', 4.0, 98.50, 98.10, 18.0, 18.0, 0.0, '1'),
('TEST005_EX_007', 'TEST005_EX', '2022-2023-1', 3.9, 97.60, 97.20, 17.5, 17.5, 0.0, '1'),
('TEST005_EX_008', 'TEST005_EX', '2022-2023-2', 4.0, 98.80, 98.40, 16.0, 16.0, 0.0, '1');

-- 相似学生：TEST005_SI
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM) VALUES
('TEST005_SI_001', 'TEST005_SI', '2019-2020-1', 3.1, 91.50, 91.10, 20.0, 20.0, 0.0, '1'),
('TEST005_SI_002', 'TEST005_SI', '2019-2020-2', 3.3, 92.20, 91.80, 18.5, 18.5, 0.0, '1'),
('TEST005_SI_003', 'TEST005_SI', '2020-2021-1', 3.5, 93.80, 93.40, 19.0, 19.0, 0.0, '1'),
('TEST005_SI_004', 'TEST005_SI', '2020-2021-2', 3.6, 94.90, 94.50, 18.0, 18.0, 0.0, '1'),
('TEST005_SI_005', 'TEST005_SI', '2021-2022-1', 3.7, 95.20, 94.80, 19.5, 19.5, 0.0, '1'),
('TEST005_SI_006', 'TEST005_SI', '2021-2022-2', 3.8, 96.10, 95.70, 18.0, 18.0, 0.0, '1'),
('TEST005_SI_007', 'TEST005_SI', '2022-2023-1', 3.7, 95.40, 95.00, 17.5, 17.5, 0.0, '1'),
('TEST005_SI_008', 'TEST005_SI', '2022-2023-2', 3.9, 96.50, 96.10, 16.0, 16.0, 0.0, '1');

-- 预警信息
INSERT INTO T_GXYS_YJXX (XH, YJLX_M, YJDJ_M, YJZT_M, MBYXSH, XSYXSH) VALUES
('TEST005', '01', '2', '01', 'TEST005_EX', 'TEST005_SI');

-- =====================================================
-- 清理测试数据的SQL（测试完成后使用）
-- =====================================================
/*
-- 清理测试数据
DELETE FROM T_GXXS_BKSXQJDXX WHERE XH LIKE 'TEST%';
DELETE FROM T_GXYS_YJXX WHERE XH LIKE 'TEST%';
*/
