<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zkdm.iai.mapper.student.UndergraduateSemesterGpaMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa">
        <id column="WYBS" property="id" />
        <result column="XH" property="studentId" />
        <result column="XNXQ" property="academicYearSemester" />
        <result column="GPA" property="gpa" />
        <result column="JQPJF" property="weightedAverageScore" />
        <result column="SSPJF" property="arithmeticAverageScore" />
        <result column="ZXF" property="totalCredits" />
        <result column="TGZXF" property="passedCredits" />
        <result column="WTCZXF" property="failedCredits" />
        <result column="SFCYPM" property="participateInRanking" />
        <result column="GPAJSFS" property="gpaCalculationMethod" />
        <result column="GXROSJ" property="updateTime" />
        <result column="TSTAMP" property="timestamp" />
    </resultMap>

    <!-- 获取学生所有学期的GPA信息 -->
    <select id="selectByStudentId" resultMap="BaseResultMap">
        SELECT 
            WYBS,
            XH,
            XNXQ,
            GPA,
            JQPJF,
            SSPJF,
            ZXF,
            TGZXF,
            WTCZXF,
            SFCYPM,
            GPAJSFS,
            GXROSJ,
            TSTAMP
        FROM T_GXXS_BKSXQJDXX 
        WHERE XH = #{studentId}
        ORDER BY XNXQ DESC
    </select>

    <!-- 获取学生指定学期的GPA信息 -->
    <select id="selectByStudentIdAndSemester" resultMap="BaseResultMap">
        SELECT 
            WYBS,
            XH,
            XNXQ,
            GPA,
            JQPJF,
            SSPJF,
            ZXF,
            TGZXF,
            WTCZXF,
            SFCYPM,
            GPAJSFS,
            GXROSJ,
            TSTAMP
        FROM T_GXXS_BKSXQJDXX 
        WHERE XH = #{studentId} AND XNXQ = #{semester}
        LIMIT 1
    </select>

    <!-- 获取学生当前已获得的总学分 -->
    <select id="selectTotalPassedCredits" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(TGZXF), 0) 
        FROM T_GXXS_BKSXQJDXX 
        WHERE XH = #{studentId}
    </select>

    <!-- 获取学生最新的GPA信息 -->
    <select id="selectLatestByStudentId" resultMap="BaseResultMap">
        SELECT 
            WYBS,
            XH,
            XNXQ,
            GPA,
            JQPJF,
            SSPJF,
            ZXF,
            TGZXF,
            WTCZXF,
            SFCYPM,
            GPAJSFS,
            GXROSJ,
            TSTAMP
        FROM T_GXXS_BKSXQJDXX 
        WHERE XH = #{studentId}
        ORDER BY XNXQ DESC
        LIMIT 1
    </select>

    <!-- 获取学生指定学期的总学分 -->
    <select id="selectPassedCreditsBySemester" resultType="java.math.BigDecimal">
        SELECT IFNULL(TGZXF, 0) 
        FROM T_GXXS_BKSXQJDXX 
        WHERE XH = #{studentId} AND XNXQ = #{semester}
        LIMIT 1
    </select>

    <!-- 获取学生上一个学期的GPA信息（用于对比） -->
    <select id="selectPreviousSemesterGpa" resultMap="BaseResultMap">
        SELECT 
            WYBS,
            XH,
            XNXQ,
            GPA,
            JQPJF,
            SSPJF,
            ZXF,
            TGZXF,
            WTCZXF,
            SFCYPM,
            GPAJSFS,
            GXROSJ,
            TSTAMP
        FROM T_GXXS_BKSXQJDXX 
        WHERE XH = #{studentId} AND XNXQ &lt; #{currentSemester}
        ORDER BY XNXQ DESC
        LIMIT 1
    </select>

</mapper>
