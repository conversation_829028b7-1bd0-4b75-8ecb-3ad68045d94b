package com.zkdm.iai.strategy.impl;

import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import com.zkdm.iai.mapper.student.UndergraduateSemesterGpaMapper;
import com.zkdm.iai.strategy.gpainfo.enums.StudentType;
import com.zkdm.iai.strategy.AbstractGpaDataStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 榜样学生GPA数据获取策略
 * <p>
 * 专门处理榜样学生的GPA数据获取逻辑，用于为预警学生提供学习目标和激励。
 * <p>
 * 业务规则：
 * - 获取榜样学生当前学期之后的GPA记录作为目标轨迹
 * - 用于展示优秀学长的学业发展路径
 * - 数据范围：预警学生对应的榜样学生的“当前学期之"后的所有学期
 * - 为预警学生提供可追求的学业目标
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExemplaryStudentGpaStrategy extends AbstractGpaDataStrategy {
    
    /**
     * 构造函数
     * 
     * @param semesterGpaMapper 学期绩点数据访问接口
     */
    public ExemplaryStudentGpaStrategy(UndergraduateSemesterGpaMapper semesterGpaMapper) {
        super(semesterGpaMapper);
    }
    
    /**
     * 获取学生类型
     * 
     * @return 榜样学生类型
     */
    @Override
    public StudentType getStudentType() {
        return StudentType.EXEMPLARY;
    }
    
    /**
     * 过滤榜样学生的GPA数据
     * <p>
     * 榜样学生的数据过滤规则：
     * 1. 返回榜样学生对应预警学生当前学期及之后的相对学期数据
     * 2. 例如：预警学生当前大二上，返回榜样学生大二上、大二下、大三上、大三下、大四上、大四下等数据
     * 3. 如果没有指定当前学期，返回所有数据
     * 4. 确保数据质量，过滤掉异常记录
     *
     * @param rawGpaList 原始GPA数据列表
     * @param currentSemester 当前学期，可以为空
     * @return 过滤后的GPA数据列表
     */
    @Override
    protected List<UndergraduateSemesterGpa> filterGpaData(
            List<UndergraduateSemesterGpa> rawGpaList,
            String currentSemester) {

        log.debug("开始过滤榜样学生GPA数据，原始数据量：{}, 当前学期：{}",
                rawGpaList.size(), currentSemester);

        // 如果没有指定当前学期，返回所有数据
        if (!StringUtils.hasText(currentSemester)) {
            log.debug("当前学期为空，返回榜样学生所有GPA数据");
            return filterValidRecords(rawGpaList);
        }

        // 计算预警学生当前学期对应的相对学期位置
        int currentSemesterIndex = calculateSemesterIndex(currentSemester);
        log.debug("当前学期{}对应的索引：{}", currentSemester, currentSemesterIndex);

        // 过滤榜样学生对应学期及之后的数据（包含当前学期）
        List<UndergraduateSemesterGpa> filteredList = rawGpaList.stream()
                .filter(this::isValidGpaRecord)
                .filter(gpa -> {
                    int gpaSemesterIndex = calculateSemesterIndex(gpa.getAcademicYearSemester());
                    boolean include = gpaSemesterIndex >= currentSemesterIndex;
                    log.trace("榜样学生学期{}（索引{}）{}",
                            gpa.getAcademicYearSemester(), gpaSemesterIndex,
                            include ? "包含" : "排除");
                    return include;
                })
                .collect(Collectors.toList());

        log.debug("榜样学生GPA数据过滤完成，过滤后数据量：{}", filteredList.size());

        // 记录目标轨迹信息
        if (!filteredList.isEmpty()) {
            String firstSemester = filteredList.get(0).getAcademicYearSemester();
            String lastSemester = filteredList.get(filteredList.size() - 1).getAcademicYearSemester();
            log.debug("榜样学生目标轨迹范围：{} 至 {}", firstSemester, lastSemester);

            // 计算平均GPA作为目标参考
            double avgGpa = filteredList.stream()
                    .filter(gpa -> gpa.getGpa() != null)
                    .mapToDouble(gpa -> gpa.getGpa().doubleValue())
                    .average()
                    .orElse(0.0);
            log.debug("榜样学生目标轨迹平均GPA：{:.2f}", avgGpa);
        }

        return filteredList;
    }
    
    /**
     * 过滤有效记录
     * <p>
     * 当没有指定当前学期时，仍需要过滤掉无效记录
     * 
     * @param rawGpaList 原始GPA数据列表
     * @return 有效记录列表
     */
    private List<UndergraduateSemesterGpa> filterValidRecords(List<UndergraduateSemesterGpa> rawGpaList) {
        return rawGpaList.stream()
                .filter(this::isValidGpaRecord)
                .collect(Collectors.toList());
    }
    
    /**
     * 验证GPA记录是否有效
     * <p>
     * 对于榜样学生，我们需要确保数据质量：
     * - 学期信息必须完整
     * - GPA值应该相对较高（体现榜样特质）
     * - 学期格式必须正确
     * 
     * @param gpa GPA记录
     * @return true表示有效，false表示无效
     */
    private boolean isValidGpaRecord(UndergraduateSemesterGpa gpa) {
        if (gpa == null) {
            return false;
        }
        
        // 检查学期信息
        if (!StringUtils.hasText(gpa.getAcademicYearSemester())) {
            log.trace("榜样学生GPA记录学期信息为空，跳过：{}", gpa.getId());
            return false;
        }
        
        // 检查学期格式
        if (!isValidSemesterFormat(gpa.getAcademicYearSemester())) {
            log.trace("榜样学生GPA记录学期格式无效，跳过：{}", gpa.getAcademicYearSemester());
            return false;
        }
        
        // 对于榜样学生，可以添加GPA质量检查
        if (gpa.getGpa() != null && gpa.getGpa().doubleValue() < 0) {
            log.trace("榜样学生GPA值异常，跳过：{}", gpa.getGpa());
            return false;
        }
        
        return true;
    }
    
    /**
     * 计算学期索引
     * <p>
     * 将学期转换为相对索引，用于比较学期的相对位置
     * 例如：2019-2020-1 -> 1, 2019-2020-2 -> 2, 2020-2021-1 -> 3
     *
     * @param semester 学期字符串，格式：YYYY-YYYY-[1|2]
     * @return 学期索引，解析失败返回0
     */
    private int calculateSemesterIndex(String semester) {
        if (!StringUtils.hasText(semester) || !isValidSemesterFormat(semester)) {
            return 0;
        }

        try {
            String[] parts = semester.split("-");
            int startYear = Integer.parseInt(parts[0]);
            int semesterNumber = Integer.parseInt(parts[2]);

            // 计算相对索引：(年份-2019)*2 + 学期号
            // 这样2019-2020-1=1, 2019-2020-2=2, 2020-2021-1=3, 2020-2021-2=4
            return (startYear - 2019) * 2 + semesterNumber;
        } catch (Exception e) {
            log.warn("计算学期{}索引失败", semester, e);
            return 0;
        }
    }

    /**
     * 验证学期格式
     *
     * @param semester 学期字符串
     * @return true表示格式正确，false表示格式错误
     */
    private boolean isValidSemesterFormat(String semester) {
        // 学期格式：YYYY-YYYY-[1|2]
        return semester.matches("\\d{4}-\\d{4}-[12]");
    }

    
    /**
     * 检查是否支持指定的学生类型
     * 
     * @param studentType 学生类型
     * @return true表示支持榜样学生类型
     */
    @Override
    public boolean supports(StudentType studentType) {
        return StudentType.EXEMPLARY == studentType;
    }
    
    /**
     * 重写参数验证，添加榜样学生特有的验证逻辑
     * 
     * @param studentId 学生学号
     * @param referenceStudentId 参考学生学号
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    @Override
    protected void validateParameters(String studentId, String referenceStudentId) {
        super.validateParameters(studentId, referenceStudentId);
        
        // 可以添加榜样学生特有的验证逻辑
        // 例如：检查学生是否确实是榜样学生
        if (!hasGpaData(studentId)) {
            log.warn("榜样学生{}没有GPA数据，可能影响目标轨迹的准确性", studentId);
        }
    }
}
