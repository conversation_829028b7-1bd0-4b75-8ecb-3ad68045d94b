package com.zkdm.iai.domain.entity.student;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * 实体类：本科生学期绩点信息 (T_GXXS_BKSXQJDXX)
 * 所属部门: 教务处
 * <AUTHOR>
 */
@Schema(description = "本科生学期绩点信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXXS_BKSXQJDXX")
public class UndergraduateSemesterGpa {

    /**
     * 唯一标识 (主键)
     */
    @Schema(description = "唯一标识", example = "JDINFO_001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 学号
     */
    @Schema(description = "学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH")
    private String studentId;

    /**
     * 学年学期
     */
    @Schema(description = "学年学期", example = "2023-2024-1", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XNXQ")
    private String academicYearSemester;

    /**
     * GPA
     */
    @Schema(description = "GPA", example = "3.85")
    @TableField("GPA")
    private BigDecimal gpa;

    /**
     * 加权平均分
     */
    @Schema(description = "加权平均分", example = "92.50")
    @TableField("JQPJF")
    private BigDecimal weightedAverageScore;

    /**
     * 算术平均分
     */
    @Schema(description = "算术平均分", example = "91.80")
    @TableField("SSPJF")
    private BigDecimal arithmeticAverageScore;

    /**
     * 总学分
     */
    @Schema(description = "总学分", example = "20.5")
    @TableField("ZXF")
    private BigDecimal totalCredits;

    /**
     * 通过总学分
     */
    @Schema(description = "通过总学分", example = "20.5")
    @TableField("TGZXF")
    private BigDecimal passedCredits;

    /**
     * 未通过总学分
     */
    @Schema(description = "未通过总学分", example = "0.0")
    @TableField("WTCZXF")
    private BigDecimal failedCredits;

    /**
     * 是否参与排名
     */
    @Schema(description = "是否参与排名 (关联字典:是/否(DM_XS_SFZDM))", example = "1")
    @TableField("SFCYPM")
    private String participateInRanking;

    /**
     * GPA计算方式
     */
    @Schema(description = "GPA计算方式", example = "标准4.0算法")
    @TableField("GPAJSFS")
    private String gpaCalculationMethod;
    
    /**
     * 更新日期时间
     */
    @Schema(description = "更新日期时间", example = "20241220120000")
    @TableField("GXROSJ")
    private String updateTime;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "20241220120000")
    @TableField("TSTAMP")
    private String timestamp;
}