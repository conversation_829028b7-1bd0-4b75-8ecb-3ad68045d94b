-- =====================================================
-- GPA分析接口测试数据脚本
-- 用于测试getGpaAnalysis接口的各种场景
-- =====================================================

-- 清理现有测试数据
DELETE FROM T_GXXS_BKSXQJDXX WHERE XH IN (
    '202310001', '202310002', '202310003', '202310004', '202310005',  -- 预警学生
    '201901002', '201901003', '201901004',                            -- 榜样学生
    '201908015', '201908016', '201908017'                             -- 相似学生
);

DELETE FROM TB_ACADEMIC_WARNING WHERE XH IN (
    '202310001', '202310002', '202310003', '202310004', '202310005'
);

-- =====================================================
-- 1. 预警学生GPA数据（2023级学生，当前大二）
-- 场景：展示学生从大一到大二的学业发展轨迹
-- =====================================================

-- 预警学生1：202310001（学业下滑型）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM, GPAJSFS, GXROSJ, TSTAMP) VALUES
('GPA_202310001_001', '202310001', '2023-2024-1', 3.2, 85.5, 84.2, 20.0, 20.0, 0.0, '1', '标准4.0算法', '20240301120000', '20240301120000'),
('GPA_202310001_002', '202310001', '2023-2024-2', 2.8, 78.3, 77.1, 18.5, 16.5, 2.0, '1', '标准4.0算法', '20240701120000', '20240701120000'),
('GPA_202310001_003', '202310001', '2024-2025-1', 2.1, 68.7, 67.5, 19.0, 15.0, 4.0, '1', '标准4.0算法', '20241201120000', '20241201120000');

-- 预警学生2：202310002（稳定低分型）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM, GPAJSFS, GXROSJ, TSTAMP) VALUES
('GPA_202310002_001', '202310002', '2023-2024-1', 2.3, 72.1, 71.8, 20.0, 18.0, 2.0, '1', '标准4.0算法', '20240301120000', '20240301120000'),
('GPA_202310002_002', '202310002', '2023-2024-2', 2.4, 73.5, 72.9, 18.5, 16.5, 2.0, '1', '标准4.0算法', '20240701120000', '20240701120000'),
('GPA_202310002_003', '202310002', '2024-2025-1', 2.2, 70.8, 70.1, 19.0, 16.0, 3.0, '1', '标准4.0算法', '20241201120000', '20241201120000');

-- 预警学生3：202310003（波动型）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM, GPAJSFS, GXROSJ, TSTAMP) VALUES
('GPA_202310003_001', '202310003', '2023-2024-1', 2.9, 79.2, 78.5, 20.0, 19.0, 1.0, '1', '标准4.0算法', '20240301120000', '20240301120000'),
('GPA_202310003_002', '202310003', '2023-2024-2', 1.8, 62.4, 61.7, 18.5, 14.5, 4.0, '1', '标准4.0算法', '20240701120000', '20240701120000'),
('GPA_202310003_003', '202310003', '2024-2025-1', 2.6, 75.3, 74.8, 19.0, 17.0, 2.0, '1', '标准4.0算法', '20241201120000', '20241201120000');

-- 预警学生4：202310004（严重预警型）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM, GPAJSFS, GXROSJ, TSTAMP) VALUES
('GPA_202310004_001', '202310004', '2023-2024-1', 1.9, 65.2, 64.8, 20.0, 16.0, 4.0, '1', '标准4.0算法', '20240301120000', '20240301120000'),
('GPA_202310004_002', '202310004', '2023-2024-2', 1.5, 58.7, 57.9, 18.5, 13.5, 5.0, '1', '标准4.0算法', '20240701120000', '20240701120000'),
('GPA_202310004_003', '202310004', '2024-2025-1', 1.2, 52.3, 51.6, 19.0, 12.0, 7.0, '1', '标准4.0算法', '20241201120000', '20241201120000');

-- 预警学生5：202310005（边缘预警型）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM, GPAJSFS, GXROSJ, TSTAMP) VALUES
('GPA_202310005_001', '202310005', '2023-2024-1', 2.7, 76.8, 76.2, 20.0, 19.0, 1.0, '1', '标准4.0算法', '20240301120000', '20240301120000'),
('GPA_202310005_002', '202310005', '2023-2024-2', 2.5, 74.1, 73.6, 18.5, 17.5, 1.0, '1', '标准4.0算法', '20240701120000', '20240701120000'),
('GPA_202310005_003', '202310005', '2024-2025-1', 2.3, 71.5, 70.9, 19.0, 17.0, 2.0, '1', '标准4.0算法', '20241201120000', '20241201120000');

-- =====================================================
-- 2. 榜样学生GPA数据（2019级学生，已毕业）
-- 场景：展示优秀学长的完整学业轨迹，为预警学生提供目标
-- =====================================================

-- 榜样学生1：201901002（持续优秀型）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM, GPAJSFS, GXROSJ, TSTAMP) VALUES
('GPA_201901002_001', '201901002', '2019-2020-1', 3.8, 92.5, 91.8, 20.0, 20.0, 0.0, '1', '标准4.0算法', '20200301120000', '20200301120000'),
('GPA_201901002_002', '201901002', '2019-2020-2', 3.9, 94.2, 93.6, 18.5, 18.5, 0.0, '1', '标准4.0算法', '20200701120000', '20200701120000'),
('GPA_201901002_003', '201901002', '2020-2021-1', 3.7, 90.8, 90.1, 19.0, 19.0, 0.0, '1', '标准4.0算法', '20201201120000', '20201201120000'),
('GPA_201901002_004', '201901002', '2020-2021-2', 3.8, 92.1, 91.5, 18.0, 18.0, 0.0, '1', '标准4.0算法', '20210601120000', '20210601120000'),
('GPA_201901002_005', '201901002', '2021-2022-1', 3.9, 94.7, 94.1, 19.5, 19.5, 0.0, '1', '标准4.0算法', '20211201120000', '20211201120000'),
('GPA_201901002_006', '201901002', '2021-2022-2', 4.0, 96.3, 95.8, 18.0, 18.0, 0.0, '1', '标准4.0算法', '20220601120000', '20220601120000'),
('GPA_201901002_007', '201901002', '2022-2023-1', 3.8, 92.9, 92.3, 17.5, 17.5, 0.0, '1', '标准4.0算法', '20221201120000', '20221201120000'),
('GPA_201901002_008', '201901002', '2022-2023-2', 3.9, 94.5, 93.9, 16.0, 16.0, 0.0, '1', '标准4.0算法', '20230601120000', '20230601120000'),
-- 添加未来学期数据用于展示目标轨迹
('GPA_201901002_009', '201901002', '2023-2024-2', 3.8, 93.2, 92.8, 15.0, 15.0, 0.0, '1', '标准4.0算法', '20240601120000', '20240601120000'),
('GPA_201901002_010', '201901002', '2024-2025-1', 3.9, 94.8, 94.2, 16.0, 16.0, 0.0, '1', '标准4.0算法', '20241201120000', '20241201120000'),
('GPA_201901002_011', '201901002', '2024-2025-2', 4.0, 96.1, 95.7, 14.0, 14.0, 0.0, '1', '标准4.0算法', '20250601120000', '20250601120000');

-- 榜样学生2：201901003（逐步提升型）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM, GPAJSFS, GXROSJ, TSTAMP) VALUES
('GPA_201901003_001', '201901003', '2019-2020-1', 3.2, 82.1, 81.5, 20.0, 19.0, 1.0, '1', '标准4.0算法', '20200301120000', '20200301120000'),
('GPA_201901003_002', '201901003', '2019-2020-2', 3.4, 84.7, 84.1, 18.5, 18.0, 0.5, '1', '标准4.0算法', '20200701120000', '20200701120000'),
('GPA_201901003_003', '201901003', '2020-2021-1', 3.6, 87.3, 86.8, 19.0, 19.0, 0.0, '1', '标准4.0算法', '20201201120000', '20201201120000'),
('GPA_201901003_004', '201901003', '2020-2021-2', 3.7, 89.1, 88.6, 18.0, 18.0, 0.0, '1', '标准4.0算法', '20210601120000', '20210601120000'),
('GPA_201901003_005', '201901003', '2021-2022-1', 3.8, 91.4, 90.9, 19.5, 19.5, 0.0, '1', '标准4.0算法', '20211201120000', '20211201120000'),
('GPA_201901003_006', '201901003', '2021-2022-2', 3.9, 93.2, 92.7, 18.0, 18.0, 0.0, '1', '标准4.0算法', '20220601120000', '20220601120000'),
('GPA_201901003_007', '201901003', '2022-2023-1', 3.8, 92.5, 92.0, 17.5, 17.5, 0.0, '1', '标准4.0算法', '20221201120000', '20221201120000'),
('GPA_201901003_008', '201901003', '2022-2023-2', 3.9, 94.1, 93.6, 16.0, 16.0, 0.0, '1', '标准4.0算法', '20230601120000', '20230601120000'),
-- 添加未来学期数据用于展示目标轨迹
('GPA_201901003_009', '201901003', '2023-2024-2', 3.7, 91.5, 90.9, 15.0, 15.0, 0.0, '1', '标准4.0算法', '20240601120000', '20240601120000'),
('GPA_201901003_010', '201901003', '2024-2025-1', 3.8, 93.1, 92.6, 16.0, 16.0, 0.0, '1', '标准4.0算法', '20241201120000', '20241201120000'),
('GPA_201901003_011', '201901003', '2024-2025-2', 3.9, 94.7, 94.1, 14.0, 14.0, 0.0, '1', '标准4.0算法', '20250601120000', '20250601120000');

-- =====================================================
-- 3. 相似学生GPA数据（2019级学生，学业水平相近）
-- 场景：展示与预警学生相似水平的学长改进轨迹
-- =====================================================

-- 相似学生1：201908015（改进成功型）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM, GPAJSFS, GXROSJ, TSTAMP) VALUES
('GPA_201908015_001', '201908015', '2019-2020-1', 2.1, 68.5, 67.9, 20.0, 16.0, 4.0, '1', '标准4.0算法', '20200301120000', '20200301120000'),
('GPA_201908015_002', '201908015', '2019-2020-2', 2.3, 71.2, 70.6, 18.5, 16.5, 2.0, '1', '标准4.0算法', '20200701120000', '20200701120000'),
('GPA_201908015_003', '201908015', '2020-2021-1', 2.6, 75.8, 75.1, 19.0, 17.0, 2.0, '1', '标准4.0算法', '20201201120000', '20201201120000'),
('GPA_201908015_004', '201908015', '2020-2021-2', 2.9, 79.4, 78.8, 18.0, 17.0, 1.0, '1', '标准4.0算法', '20210601120000', '20210601120000'),
('GPA_201908015_005', '201908015', '2021-2022-1', 3.1, 82.7, 82.1, 19.5, 18.5, 1.0, '1', '标准4.0算法', '20211201120000', '20211201120000'),
('GPA_201908015_006', '201908015', '2021-2022-2', 3.3, 85.1, 84.6, 18.0, 17.5, 0.5, '1', '标准4.0算法', '20220601120000', '20220601120000'),
('GPA_201908015_007', '201908015', '2022-2023-1', 3.4, 86.8, 86.2, 17.5, 17.0, 0.5, '1', '标准4.0算法', '20221201120000', '20221201120000'),
('GPA_201908015_008', '201908015', '2022-2023-2', 3.5, 88.3, 87.7, 16.0, 15.5, 0.5, '1', '标准4.0算法', '20230601120000', '20230601120000'),
-- 添加未来学期数据用于展示参考轨迹
('GPA_201908015_009', '201908015', '2023-2024-2', 3.4, 86.9, 86.3, 15.0, 14.5, 0.5, '1', '标准4.0算法', '20240601120000', '20240601120000'),
('GPA_201908015_010', '201908015', '2024-2025-1', 3.6, 89.1, 88.5, 16.0, 15.5, 0.5, '1', '标准4.0算法', '20241201120000', '20241201120000'),
('GPA_201908015_011', '201908015', '2024-2025-2', 3.7, 90.8, 90.2, 14.0, 13.5, 0.5, '1', '标准4.0算法', '20250601120000', '20250601120000');

-- 相似学生2：201908016（稳步改善型）
INSERT INTO T_GXXS_BKSXQJDXX (WYBS, XH, XNXQ, GPA, JQPJF, SSPJF, ZXF, TGZXF, WTCZXF, SFCYPM, GPAJSFS, GXROSJ, TSTAMP) VALUES
('GPA_201908016_001', '201908016', '2019-2020-1', 2.4, 72.3, 71.8, 20.0, 17.0, 3.0, '1', '标准4.0算法', '20200301120000', '20200301120000'),
('GPA_201908016_002', '201908016', '2019-2020-2', 2.5, 73.9, 73.2, 18.5, 16.5, 2.0, '1', '标准4.0算法', '20200701120000', '20200701120000'),
('GPA_201908016_003', '201908016', '2020-2021-1', 2.7, 76.4, 75.9, 19.0, 17.5, 1.5, '1', '标准4.0算法', '20201201120000', '20201201120000'),
('GPA_201908016_004', '201908016', '2020-2021-2', 2.8, 78.1, 77.6, 18.0, 17.0, 1.0, '1', '标准4.0算法', '20210601120000', '20210601120000'),
('GPA_201908016_005', '201908016', '2021-2022-1', 3.0, 80.5, 79.9, 19.5, 18.0, 1.5, '1', '标准4.0算法', '20211201120000', '20211201120000'),
('GPA_201908016_006', '201908016', '2021-2022-2', 3.1, 82.2, 81.7, 18.0, 17.5, 0.5, '1', '标准4.0算法', '20220601120000', '20220601120000'),
('GPA_201908016_007', '201908016', '2022-2023-1', 3.2, 83.8, 83.3, 17.5, 17.0, 0.5, '1', '标准4.0算法', '20221201120000', '20221201120000'),
('GPA_201908016_008', '201908016', '2022-2023-2', 3.3, 85.4, 84.9, 16.0, 15.5, 0.5, '1', '标准4.0算法', '20230601120000', '20230601120000'),
-- 添加未来学期数据用于展示参考轨迹
('GPA_201908016_009', '201908016', '2023-2024-2', 3.2, 84.1, 83.6, 15.0, 14.5, 0.5, '1', '标准4.0算法', '20240601120000', '20240601120000'),
('GPA_201908016_010', '201908016', '2024-2025-1', 3.4, 86.7, 86.1, 16.0, 15.5, 0.5, '1', '标准4.0算法', '20241201120000', '20241201120000'),
('GPA_201908016_011', '201908016', '2024-2025-2', 3.5, 88.2, 87.7, 14.0, 13.5, 0.5, '1', '标准4.0算法', '20250601120000', '20250601120000');

-- =====================================================
-- 4. 学业预警信息数据
-- 建立预警学生与榜样学生、相似学生的关联关系
-- =====================================================

INSERT INTO TB_ACADEMIC_WARNING (WYBS, XH, WARNING_TYPE, WARNING_STATUS, SIMILARITY_STUDENT_XH, EXAMPLE_STUDENT_XH, EXAMPLE_DIFFERENCES, GUIDANCE) VALUES
('WARN_TEST_001', '202310001', '01', 1, '201908015', '201901002', '榜样学长平均绩点3.8，您当前为2.1，差距1.7分。榜样学长无挂科记录，您有4门课程不及格。', '建议重点复习不及格课程，制定详细的学习计划，每周至少保证25小时的专业课学习时间。'),
('WARN_TEST_002', '202310002', '01', 1, '201908016', '201901003', '榜样学长平均绩点3.6，您当前为2.2，差距1.4分。榜样学长大二后期显著提升。', '参考相似学长的改进轨迹，重点加强基础课程学习，建议参加学习小组。'),
('WARN_TEST_003', '202310003', '01', 1, '201908015', '201901002', '榜样学长学业表现稳定，您的成绩波动较大。需要提高学习的持续性。', '建立稳定的学习习惯，避免临时抱佛脚，制定长期学习规划。'),
('WARN_TEST_004', '202310004', '01', 1, '201908015', '201901003', '您的情况较为严重，但相似学长从2.1提升到3.5，证明改进是可能的。', '立即制定学业改进计划，考虑申请学业辅导，必要时可申请重修课程。'),
('WARN_TEST_005', '202310005', '01', 1, '201908016', '201901002', '您处于预警边缘，参考榜样学长的优秀表现，仍有很大提升空间。', '保持当前学习状态，适当增加学习强度，重点关注专业核心课程。');

-- =====================================================
-- 测试场景说明
-- =====================================================
/*
测试场景覆盖：

1. 预警学生类型：
   - 学业下滑型（202310001）：从3.2降到2.1
   - 稳定低分型（202310002）：一直在2.2-2.4之间
   - 波动型（202310003）：成绩忽高忽低
   - 严重预警型（202310004）：持续低分，大量挂科
   - 边缘预警型（202310005）：刚达到预警线

2. 榜样学生类型：
   - 持续优秀型（201901002）：一直保持高GPA
   - 逐步提升型（201901003）：从3.2提升到3.9

3. 相似学生类型：
   - 改进成功型（201908015）：从2.1提升到3.5
   - 稳步改善型（201908016）：从2.4稳步提升到3.3

4. 时间范围测试：
   - 预警学生：2023-2024-1 到 2024-2025-1（当前学期）
   - 榜样/相似学生：完整的四年学业轨迹

5. 数据质量测试：
   - 包含正常GPA值
   - 包含边界值（1.2, 4.0）
   - 包含挂科情况（WTCZXF > 0）
   - 包含学分变化

使用方法：
1. 执行此脚本插入测试数据
2. 调用接口测试不同学生的GPA分析
3. 验证返回的三条轨迹曲线是否正确
4. 测试异常情况（不存在的学生等）
*/
