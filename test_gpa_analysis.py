#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPA分析功能测试脚本
用于测试修复后的GPA分析接口是否正常工作

测试场景：
1. 预警学生当前大一下 - 应该返回榜样/相似学生从大一下到大四下的数据
2. 预警学生当前大二上 - 应该返回榜样/相似学生从大二上到大四下的数据
3. 预警学生当前大三上 - 应该返回榜样/相似学生从大三上到大四下的数据
4. 预警学生当前大四上 - 应该返回榜样/相似学生从大四上到大四下的数据
5. 预警学生当前大一上 - 应该返回榜样/相似学生从大一上到大四下的数据

作者: lqj
日期: 2024-08-06
"""

import requests
import json
import time
from typing import Dict, List, Any

class GpaAnalysisTest:
    def __init__(self, base_url: str = "http://localhost:8080"):
        """
        初始化测试类
        
        Args:
            base_url: API服务的基础URL
        """
        self.base_url = base_url
        self.api_path = "/api/v1/warnings/academic"
        
    def test_gpa_analysis(self, student_id: str, expected_scenario: str, timestamp: int = None) -> Dict[str, Any]:
        """
        测试GPA分析接口
        
        Args:
            student_id: 学生学号
            expected_scenario: 期望的测试场景描述
            timestamp: 时间戳（可选）
            
        Returns:
            测试结果字典
        """
        url = f"{self.base_url}{self.api_path}/{student_id}/gpa-analysis"
        params = {}
        if timestamp:
            params['timestamp'] = timestamp
            
        try:
            print(f"\n{'='*60}")
            print(f"测试场景: {expected_scenario}")
            print(f"学生学号: {student_id}")
            print(f"请求URL: {url}")
            if params:
                print(f"请求参数: {params}")
            
            response = requests.get(url, params=params, timeout=30)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                return self._analyze_response(data, expected_scenario)
            else:
                print(f"请求失败: {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "scenario": expected_scenario
                }
                
        except Exception as e:
            print(f"请求异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "scenario": expected_scenario
            }
    
    def _analyze_response(self, data: Dict[str, Any], scenario: str) -> Dict[str, Any]:
        """
        分析响应数据
        
        Args:
            data: API响应数据
            scenario: 测试场景
            
        Returns:
            分析结果
        """
        result = {
            "success": True,
            "scenario": scenario,
            "analysis": {}
        }
        
        try:
            if data.get("code") != 200:
                result["success"] = False
                result["error"] = f"业务错误: {data.get('msg', '未知错误')}"
                return result
            
            gpa_data = data.get("data", {})
            
            # 分析预警学生数据
            warning_gpa = gpa_data.get("warningStudentGpa", [])
            result["analysis"]["warning_student"] = {
                "count": len(warning_gpa),
                "semesters": [item.get("semester") for item in warning_gpa],
                "gpa_values": [item.get("gpa") for item in warning_gpa]
            }
            
            # 分析榜样学生数据
            exemplary_gpa = gpa_data.get("exemplaryStudentGpa", [])
            result["analysis"]["exemplary_student"] = {
                "count": len(exemplary_gpa),
                "semesters": [item.get("semester") for item in exemplary_gpa],
                "gpa_values": [item.get("gpa") for item in exemplary_gpa]
            }
            
            # 分析相似学生数据
            similar_gpa = gpa_data.get("similarStudentGpa", [])
            result["analysis"]["similar_student"] = {
                "count": len(similar_gpa),
                "semesters": [item.get("semester") for item in similar_gpa],
                "gpa_values": [item.get("gpa") for item in similar_gpa]
            }
            
            # 打印分析结果
            self._print_analysis(result["analysis"])
            
            # 验证数据完整性
            validation_result = self._validate_data(result["analysis"], scenario)
            result["validation"] = validation_result
            
            if not validation_result["passed"]:
                result["success"] = False
                result["error"] = validation_result["message"]
            
        except Exception as e:
            result["success"] = False
            result["error"] = f"数据分析异常: {str(e)}"
        
        return result
    
    def _print_analysis(self, analysis: Dict[str, Any]):
        """打印分析结果"""
        print(f"\n数据分析结果:")
        
        for student_type, data in analysis.items():
            type_name = {
                "warning_student": "预警学生",
                "exemplary_student": "榜样学生", 
                "similar_student": "相似学生"
            }.get(student_type, student_type)
            
            print(f"\n{type_name}:")
            print(f"  数据条数: {data['count']}")
            print(f"  学期范围: {data['semesters']}")
            print(f"  GPA值: {data['gpa_values']}")
    
    def _validate_data(self, analysis: Dict[str, Any], scenario: str) -> Dict[str, Any]:
        """
        验证数据完整性
        
        Args:
            analysis: 分析结果
            scenario: 测试场景
            
        Returns:
            验证结果
        """
        validation = {
            "passed": True,
            "message": "数据验证通过",
            "details": []
        }
        
        try:
            # 基本数据存在性检查
            if analysis["warning_student"]["count"] == 0:
                validation["details"].append("预警学生数据为空")
            
            if analysis["exemplary_student"]["count"] == 0:
                validation["details"].append("榜样学生数据为空")
                
            if analysis["similar_student"]["count"] == 0:
                validation["details"].append("相似学生数据为空")
            
            # 根据场景验证数据范围
            if "大一下" in scenario:
                # 榜样学生和相似学生应该从大一下开始有数据
                expected_semesters = ["大一下", "大二上", "大二下", "大三上", "大三下", "大四上", "大四下"]
                self._check_semester_coverage(analysis["exemplary_student"]["semesters"], 
                                            expected_semesters, "榜样学生", validation)
                self._check_semester_coverage(analysis["similar_student"]["semesters"], 
                                            expected_semesters, "相似学生", validation)
            
            elif "大二上" in scenario:
                expected_semesters = ["大二上", "大二下", "大三上", "大三下", "大四上", "大四下"]
                self._check_semester_coverage(analysis["exemplary_student"]["semesters"], 
                                            expected_semesters, "榜样学生", validation)
                self._check_semester_coverage(analysis["similar_student"]["semesters"], 
                                            expected_semesters, "相似学生", validation)
            
            elif "大三上" in scenario:
                expected_semesters = ["大三上", "大三下", "大四上", "大四下"]
                self._check_semester_coverage(analysis["exemplary_student"]["semesters"], 
                                            expected_semesters, "榜样学生", validation)
                self._check_semester_coverage(analysis["similar_student"]["semesters"], 
                                            expected_semesters, "相似学生", validation)
            
            elif "大四上" in scenario:
                expected_semesters = ["大四上", "大四下"]
                self._check_semester_coverage(analysis["exemplary_student"]["semesters"], 
                                            expected_semesters, "榜样学生", validation)
                self._check_semester_coverage(analysis["similar_student"]["semesters"], 
                                            expected_semesters, "相似学生", validation)
            
            elif "大一上" in scenario:
                expected_semesters = ["大一上", "大一下", "大二上", "大二下", "大三上", "大三下", "大四上", "大四下"]
                self._check_semester_coverage(analysis["exemplary_student"]["semesters"], 
                                            expected_semesters, "榜样学生", validation)
                self._check_semester_coverage(analysis["similar_student"]["semesters"], 
                                            expected_semesters, "相似学生", validation)
            
            if validation["details"]:
                validation["passed"] = False
                validation["message"] = "; ".join(validation["details"])
                
        except Exception as e:
            validation["passed"] = False
            validation["message"] = f"验证过程异常: {str(e)}"
        
        return validation
    
    def _check_semester_coverage(self, actual_semesters: List[str], expected_semesters: List[str], 
                                student_type: str, validation: Dict[str, Any]):
        """检查学期覆盖情况"""
        missing_semesters = []
        for semester in expected_semesters:
            if semester not in actual_semesters:
                missing_semesters.append(semester)
        
        if missing_semesters:
            validation["details"].append(f"{student_type}缺少学期数据: {missing_semesters}")
    
    def run_all_tests(self):
        """运行所有测试用例"""
        test_cases = [
            {
                "student_id": "TEST001",
                "scenario": "预警学生当前大一下（2019-2020-2）",
                "timestamp": 1577836800000  # 2020-01-01的时间戳，对应大一下学期
            },
            {
                "student_id": "TEST002", 
                "scenario": "预警学生当前大二上（2020-2021-1）",
                "timestamp": 1598918400000  # 2020-09-01的时间戳，对应大二上学期
            },
            {
                "student_id": "TEST003",
                "scenario": "预警学生当前大三上（2021-2022-1）", 
                "timestamp": 1630454400000  # 2021-09-01的时间戳，对应大三上学期
            },
            {
                "student_id": "TEST004",
                "scenario": "预警学生当前大四上（2022-2023-1）",
                "timestamp": 1661990400000  # 2022-09-01的时间戳，对应大四上学期
            },
            {
                "student_id": "TEST005",
                "scenario": "预警学生当前大一上（2019-2020-1）",
                "timestamp": 1567267200000  # 2019-09-01的时间戳，对应大一上学期
            }
        ]
        
        results = []
        passed_count = 0
        
        print("开始执行GPA分析功能测试...")
        print(f"共{len(test_cases)}个测试用例")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n执行测试用例 {i}/{len(test_cases)}")
            result = self.test_gpa_analysis(**test_case)
            results.append(result)
            
            if result["success"]:
                passed_count += 1
                print("✅ 测试通过")
            else:
                print(f"❌ 测试失败: {result.get('error', '未知错误')}")
            
            # 避免请求过于频繁
            time.sleep(1)
        
        # 输出测试总结
        print(f"\n{'='*60}")
        print("测试总结")
        print(f"{'='*60}")
        print(f"总测试用例: {len(test_cases)}")
        print(f"通过用例: {passed_count}")
        print(f"失败用例: {len(test_cases) - passed_count}")
        print(f"通过率: {passed_count/len(test_cases)*100:.1f}%")
        
        # 输出失败的测试用例详情
        failed_tests = [r for r in results if not r["success"]]
        if failed_tests:
            print(f"\n失败的测试用例:")
            for test in failed_tests:
                print(f"- {test['scenario']}: {test.get('error', '未知错误')}")
        
        return results

if __name__ == "__main__":
    # 创建测试实例
    tester = GpaAnalysisTest()
    
    # 运行所有测试
    results = tester.run_all_tests()
    
    # 可以根据需要保存测试结果
    with open("test_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
