#!/bin/bash

# GPA分析功能测试脚本
# 用于测试修复后的GPA分析接口是否正常工作
# 作者: lqj
# 日期: 2024-08-06

# 配置
BASE_URL="http://localhost:8080"
API_PATH="/api/v1/warnings/academic"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试函数
test_gpa_analysis() {
    local student_id=$1
    local scenario=$2
    local timestamp=$3
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo ""
    echo "============================================================"
    log_info "测试场景: $scenario"
    log_info "学生学号: $student_id"
    
    # 构建请求URL
    local url="${BASE_URL}${API_PATH}/${student_id}/gpa-analysis"
    if [ ! -z "$timestamp" ]; then
        url="${url}?timestamp=${timestamp}"
    fi
    
    log_info "请求URL: $url"
    
    # 发送请求
    local response=$(curl -s -w "\n%{http_code}" "$url")
    local http_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n -1)
    
    log_info "响应状态码: $http_code"
    
    if [ "$http_code" -eq 200 ]; then
        # 解析JSON响应
        local code=$(echo "$body" | jq -r '.code // empty')
        local msg=$(echo "$body" | jq -r '.msg // empty')
        
        if [ "$code" = "200" ]; then
            log_success "接口调用成功: $msg"
            
            # 分析数据
            analyze_response "$body" "$scenario"
            
        else
            log_error "业务错误 (code: $code): $msg"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        log_error "HTTP错误 ($http_code): $body"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# 分析响应数据
analyze_response() {
    local response=$1
    local scenario=$2
    
    # 提取各类学生的GPA数据
    local warning_count=$(echo "$response" | jq '.data.warningStudentGpa | length')
    local exemplary_count=$(echo "$response" | jq '.data.exemplaryStudentGpa | length')
    local similar_count=$(echo "$response" | jq '.data.similarStudentGpa | length')
    
    log_info "数据分析结果:"
    echo "  预警学生数据条数: $warning_count"
    echo "  榜样学生数据条数: $exemplary_count"
    echo "  相似学生数据条数: $similar_count"
    
    # 提取学期信息
    local warning_semesters=$(echo "$response" | jq -r '.data.warningStudentGpa[].semester' | tr '\n' ' ')
    local exemplary_semesters=$(echo "$response" | jq -r '.data.exemplaryStudentGpa[].semester' | tr '\n' ' ')
    local similar_semesters=$(echo "$response" | jq -r '.data.similarStudentGpa[].semester' | tr '\n' ' ')
    
    echo "  预警学生学期: $warning_semesters"
    echo "  榜样学生学期: $exemplary_semesters"
    echo "  相似学生学期: $similar_semesters"
    
    # 验证数据完整性
    validate_data "$scenario" "$exemplary_count" "$similar_count" "$exemplary_semesters" "$similar_semesters"
}

# 验证数据完整性
validate_data() {
    local scenario=$1
    local exemplary_count=$2
    local similar_count=$3
    local exemplary_semesters=$4
    local similar_semesters=$5
    
    local validation_passed=true
    local validation_messages=()
    
    # 基本数据存在性检查
    if [ "$exemplary_count" -eq 0 ]; then
        validation_messages+=("榜样学生数据为空")
        validation_passed=false
    fi
    
    if [ "$similar_count" -eq 0 ]; then
        validation_messages+=("相似学生数据为空")
        validation_passed=false
    fi
    
    # 根据场景验证数据范围
    case "$scenario" in
        *"大一下"*)
            check_semester_coverage "$exemplary_semesters" "大一下 大二上 大二下 大三上 大三下 大四上 大四下" "榜样学生" validation_messages validation_passed
            check_semester_coverage "$similar_semesters" "大一下 大二上 大二下 大三上 大三下 大四上 大四下" "相似学生" validation_messages validation_passed
            ;;
        *"大二上"*)
            check_semester_coverage "$exemplary_semesters" "大二上 大二下 大三上 大三下 大四上 大四下" "榜样学生" validation_messages validation_passed
            check_semester_coverage "$similar_semesters" "大二上 大二下 大三上 大三下 大四上 大四下" "相似学生" validation_messages validation_passed
            ;;
        *"大三上"*)
            check_semester_coverage "$exemplary_semesters" "大三上 大三下 大四上 大四下" "榜样学生" validation_messages validation_passed
            check_semester_coverage "$similar_semesters" "大三上 大三下 大四上 大四下" "相似学生" validation_messages validation_passed
            ;;
        *"大四上"*)
            check_semester_coverage "$exemplary_semesters" "大四上 大四下" "榜样学生" validation_messages validation_passed
            check_semester_coverage "$similar_semesters" "大四上 大四下" "相似学生" validation_messages validation_passed
            ;;
        *"大一上"*)
            check_semester_coverage "$exemplary_semesters" "大一上 大一下 大二上 大二下 大三上 大三下 大四上 大四下" "榜样学生" validation_messages validation_passed
            check_semester_coverage "$similar_semesters" "大一上 大一下 大二上 大二下 大三上 大三下 大四上 大四下" "相似学生" validation_messages validation_passed
            ;;
    esac
    
    # 输出验证结果
    if [ "$validation_passed" = true ]; then
        log_success "数据验证通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        log_error "数据验证失败:"
        for msg in "${validation_messages[@]}"; do
            echo "    - $msg"
        done
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# 检查学期覆盖情况
check_semester_coverage() {
    local actual_semesters=$1
    local expected_semesters=$2
    local student_type=$3
    local -n messages=$4
    local -n passed=$5
    
    local missing_semesters=()
    
    for expected in $expected_semesters; do
        if [[ ! " $actual_semesters " =~ " $expected " ]]; then
            missing_semesters+=("$expected")
        fi
    done
    
    if [ ${#missing_semesters[@]} -gt 0 ]; then
        messages+=("${student_type}缺少学期数据: ${missing_semesters[*]}")
        passed=false
    fi
}

# 检查依赖
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        log_error "curl 命令未找到，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 命令未找到，请先安装 jq"
        exit 1
    fi
}

# 主函数
main() {
    echo "GPA分析功能测试脚本"
    echo "===================="
    
    # 检查依赖
    check_dependencies
    
    # 测试用例
    log_info "开始执行测试用例..."
    
    # 测试用例1：预警学生当前大一下
    test_gpa_analysis "TEST001" "预警学生当前大一下（2019-2020-2）" "1577836800000"
    
    # 测试用例2：预警学生当前大二上
    test_gpa_analysis "TEST002" "预警学生当前大二上（2020-2021-1）" "1598918400000"
    
    # 测试用例3：预警学生当前大三上
    test_gpa_analysis "TEST003" "预警学生当前大三上（2021-2022-1）" "1630454400000"
    
    # 测试用例4：预警学生当前大四上
    test_gpa_analysis "TEST004" "预警学生当前大四上（2022-2023-1）" "1661990400000"
    
    # 测试用例5：预警学生当前大一上
    test_gpa_analysis "TEST005" "预警学生当前大一上（2019-2020-1）" "1567267200000"
    
    # 输出测试总结
    echo ""
    echo "============================================================"
    log_info "测试总结"
    echo "============================================================"
    echo "总测试用例: $TOTAL_TESTS"
    echo "通过用例: $PASSED_TESTS"
    echo "失败用例: $FAILED_TESTS"
    
    if [ $TOTAL_TESTS -gt 0 ]; then
        local pass_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
        echo "通过率: ${pass_rate}%"
        
        if [ $FAILED_TESTS -eq 0 ]; then
            log_success "所有测试用例通过！"
            exit 0
        else
            log_warning "存在失败的测试用例，请检查日志"
            exit 1
        fi
    else
        log_error "没有执行任何测试用例"
        exit 1
    fi
}

# 执行主函数
main "$@"
