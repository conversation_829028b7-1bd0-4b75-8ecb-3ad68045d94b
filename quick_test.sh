#!/bin/bash

# 快速测试GPA分析功能
# 使用正确的表名和字段名

BASE_URL="http://localhost:8080"
API_PATH="/api/v1/warnings/academic"

echo "🚀 GPA分析功能快速测试"
echo "======================="
echo ""

# 测试函数
test_single() {
    local student_id=$1
    local scenario=$2
    local timestamp=$3
    
    echo "📋 测试: $scenario"
    echo "👤 学号: $student_id"
    
    local url="${BASE_URL}${API_PATH}/${student_id}/gpa-analysis"
    if [ ! -z "$timestamp" ]; then
        url="${url}?timestamp=${timestamp}"
    fi
    
    echo "🌐 URL: $url"
    echo ""
    
    # 发送请求
    local response=$(curl -s "$url")
    
    # 检查响应
    if echo "$response" | grep -q '"code":200'; then
        echo "✅ 接口调用成功"
        
        # 提取数据统计
        local warning_count=$(echo "$response" | grep -o '"warningStudentGpa":\[.*\]' | grep -o '{"gpa"' | wc -l)
        local exemplary_count=$(echo "$response" | grep -o '"exemplaryStudentGpa":\[.*\]' | grep -o '{"gpa"' | wc -l)
        local similar_count=$(echo "$response" | grep -o '"similarStudentGpa":\[.*\]' | grep -o '{"gpa"' | wc -l)
        
        echo "📊 数据统计:"
        echo "   预警学生: $warning_count 条记录"
        echo "   榜样学生: $exemplary_count 条记录"
        echo "   相似学生: $similar_count 条记录"
        
        # 简单验证
        if [ "$exemplary_count" -gt 0 ] && [ "$similar_count" -gt 0 ]; then
            echo "✅ 数据验证通过"
        else
            echo "❌ 数据验证失败：榜样或相似学生数据为空"
        fi
    else
        echo "❌ 接口调用失败"
        echo "📄 响应内容: $response"
    fi
    
    echo ""
    echo "----------------------------------------"
    echo ""
}

# 执行测试
echo "开始执行测试用例..."
echo ""

# 测试用例1：预警学生当前大一下
test_single "201910001" "预警学生当前大一下（2019-2020-2）" "1577836800000"

# 测试用例2：预警学生当前大二上
test_single "201910004" "预警学生当前大二上（2020-2021-1）" "1598918400000"

# 测试用例3：预警学生当前大三上
test_single "201910007" "预警学生当前大三上（2021-2022-1）" "1630454400000"

echo "🎉 测试完成！"
echo ""
echo "💡 验证要点："
echo "1. 接口返回状态码应为200"
echo "2. 榜样学生和相似学生应返回从当前学期到大四下的数据"
echo "3. 预警学生应返回从大一上到当前学期的数据"
echo ""
echo "🔧 如果测试失败，请检查："
echo "1. 数据库中是否已插入测试数据"
echo "2. 应用是否已重启"
echo "3. 数据库连接是否正常"
